<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Manager - CGT Calculator Integration</title>
    <!-- Chart.js for visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- PDF Generation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #e0e0e0;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(30, 30, 50, 0.9);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        h1 {
            text-align: center;
            color: #4CAF50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .section {
            background: rgba(40, 40, 60, 0.8);
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
            border: 1px solid #4CAF50;
        }

        .section-title {
            color: #4CAF50;
            font-size: 1.4em;
            margin-bottom: 15px;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 5px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #b0b0b0;
            font-weight: 500;
        }

        input[type="file"], select {
            width: 100%;
            padding: 12px;
            background: rgba(60, 60, 80, 0.8);
            border: 1px solid #555;
            border-radius: 8px;
            color: #e0e0e0;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="file"]:focus, select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
        }

        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        button:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 25px;
        }

        .chart-container {
            background: rgba(50, 50, 70, 0.8);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #2196F3;
        }

        .chart-title {
            color: #2196F3;
            font-size: 1.2em;
            margin-bottom: 15px;
            text-align: center;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .summary-card {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            color: white;
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
        }

        .summary-card h3 {
            font-size: 0.9em;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .summary-card .value {
            font-size: 1.8em;
            font-weight: bold;
        }

        .profit-card {
            background: linear-gradient(135deg, #4CAF50, #45a049) !important;
        }

        .loss-card {
            background: linear-gradient(135deg, #f44336, #d32f2f) !important;
        }

        .neutral-card {
            background: linear-gradient(135deg, #757575, #616161) !important;
        }

        .holdings-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(50, 50, 70, 0.8);
            border-radius: 8px;
            overflow: hidden;
        }

        .holdings-table th,
        .holdings-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #555;
        }

        .holdings-table th {
            background: rgba(76, 175, 80, 0.8);
            color: white;
            font-weight: 600;
        }

        .holdings-table tr:hover {
            background: rgba(76, 175, 80, 0.1);
        }

        .gain {
            color: #4CAF50;
            font-weight: bold;
        }

        .loss {
            color: #f44336;
            font-weight: bold;
        }

        .sold-checkbox {
            transform: scale(1.2);
            cursor: pointer;
        }

        .sold-checkbox:checked {
            accent-color: #f44336;
        }

        .sold-transaction {
            background: rgba(128, 128, 128, 0.3) !important;
            opacity: 0.7;
        }

        .hidden {
            display: none;
        }

        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #ffcdd2;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #c8e6c9;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .sync-button {
            background: linear-gradient(45deg, #FF9800, #F57C00);
        }

        .sync-button:hover {
            background: linear-gradient(45deg, #F57C00, #FF9800);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .summary-cards {
                grid-template-columns: 1fr;
            }
            
            .holdings-table {
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Portfolio Manager</h1>

        <!-- Navigation -->
        <div style="background: rgba(40, 40, 60, 0.8); padding: 15px; border-radius: 10px; margin-bottom: 25px; border: 1px solid #2196F3; text-align: center;">
            <a href="portfolio-manager.html" style="display: inline-block; background: linear-gradient(45deg, #4CAF50, #45a049); color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 0 10px; transition: all 0.3s; font-weight: 600;">📊 Portfolio Manager</a>
            <a href="cgt-calculator.html" style="display: inline-block; background: linear-gradient(45deg, #2196F3, #1976D2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 0 10px; transition: all 0.3s; font-weight: 600;">📈 CGT Calculator</a>
            <a href="ato-tax-calculator.html" style="display: inline-block; background: linear-gradient(45deg, #FF9800, #F57C00); color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 0 10px; transition: all 0.3s; font-weight: 600;">🧮 Tax Return Calculator</a>
        </div>

        <!-- Portfolio Upload Section -->
        <div class="section">
            <h2 class="section-title">Portfolio Data Upload</h2>
            
            <div class="input-group">
                <label for="portfolioFile">Upload Portfolio CSV File:</label>
                <input type="file" id="portfolioFile" accept=".csv" />
                <small style="color: #888; display: block; margin-top: 5px;">
                    Expected columns: Date, Code, Action, Quantity, Avg. price, Fees, Settl. value
                </small>
            </div>

            <button onclick="loadPortfolioData()">📊 Load Portfolio</button>
            <button onclick="syncWithCGTCalculator()" class="sync-button">🔄 Sync with CGT Calculator</button>
        </div>

        <!-- Portfolio Summary Cards -->
        <div id="summarySection" class="hidden">
            <div class="summary-cards">
                <div class="summary-card">
                    <h3>Total Portfolio Value</h3>
                    <div class="value" id="totalValue">$0.00</div>
                </div>
                <div class="summary-card">
                    <h3>Total Cost Base</h3>
                    <div class="value" id="totalCostBase">$0.00</div>
                </div>
                <div class="summary-card">
                    <h3>Unrealized Gain/Loss</h3>
                    <div class="value" id="unrealizedGainLoss">$0.00</div>
                </div>
                <div class="summary-card">
                    <h3>Number of Holdings</h3>
                    <div class="value" id="numberOfHoldings">0</div>
                </div>
            </div>
        </div>

        <!-- Visual Dashboard -->
        <div id="dashboardSection" class="hidden">
            <div class="dashboard-grid">
                <div class="chart-container">
                    <h3 class="chart-title">Portfolio Allocation</h3>
                    <canvas id="allocationChart" width="400" height="400"></canvas>
                </div>
                <div class="chart-container">
                    <h3 class="chart-title">Gains/Losses by Stock</h3>
                    <canvas id="gainsChart" width="400" height="400"></canvas>
                </div>
            </div>
        </div>

        <!-- Holdings Management Section -->
        <div id="holdingsSection" class="section hidden">
            <h2 class="section-title">Current Holdings & Transaction Management</h2>

            <div style="margin-bottom: 20px;">
                <p style="color: #FFC107; font-size: 0.9em;">
                    💡 <strong>Tip:</strong> Use the "Sold ✓" checkbox on buy transactions to mark shares as already sold.
                    This will exclude them from CGT calculations and sync with the CGT Calculator.
                </p>
            </div>

            <table class="holdings-table" id="holdingsTable">
                <thead>
                    <tr>
                        <th>Stock Code</th>
                        <th>Date Purchased</th>
                        <th>Quantity</th>
                        <th>Avg. Price</th>
                        <th>Cost Base</th>
                        <th>Current Value</th>
                        <th>Unrealized Gain/Loss</th>
                        <th>Sold ✓</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="holdingsBody">
                </tbody>
            </table>
        </div>

        <!-- Integration Status -->
        <div id="integrationSection" class="section hidden">
            <h2 class="section-title">CGT Calculator Integration</h2>

            <div id="integrationStatus">
                <p style="color: #b0b0b0;">Integration status will appear here after loading portfolio data.</p>
            </div>

            <div style="margin-top: 20px;">
                <button onclick="exportToCGTCalculator()" class="sync-button">📤 Export to CGT Calculator</button>
                <button onclick="importFromCGTCalculator()" class="sync-button">📥 Import from CGT Calculator</button>
                <button onclick="generatePortfolioReport()">📄 Generate Portfolio Report</button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let portfolioData = [];
        let currentHoldings = [];
        let allocationChart = null;
        let gainsChart = null;

        // Portfolio data processing functions
        function parseCSV(csvText) {
            const lines = csvText.trim().split('\n');
            const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));

            const data = [];
            for (let i = 1; i < lines.length; i++) {
                const values = parseCSVLine(lines[i]);
                if (values.length === headers.length) {
                    const row = {};
                    headers.forEach((header, index) => {
                        row[header] = values[index].trim().replace(/"/g, '');
                    });
                    data.push(row);
                }
            }
            return data;
        }

        function parseCSVLine(line) {
            const result = [];
            let current = '';
            let inQuotes = false;

            for (let i = 0; i < line.length; i++) {
                const char = line[i];
                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    result.push(current);
                    current = '';
                } else {
                    current += char;
                }
            }
            result.push(current);
            return result;
        }

        function parseDate(dateStr) {
            // Handle various date formats
            const formats = [
                /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/, // DD/MM/YYYY or D/M/YYYY
                /^(\d{4})-(\d{1,2})-(\d{1,2})$/, // YYYY-MM-DD
            ];

            for (let format of formats) {
                const match = dateStr.match(format);
                if (match) {
                    if (format === formats[0]) {
                        // DD/MM/YYYY format
                        return new Date(match[3], match[2] - 1, match[1]);
                    } else {
                        // YYYY-MM-DD format
                        return new Date(match[1], match[2] - 1, match[3]);
                    }
                }
            }

            // Fallback to Date constructor
            return new Date(dateStr);
        }

        function loadPortfolioData() {
            const fileInput = document.getElementById('portfolioFile');
            if (!fileInput.files.length) {
                showError('Please select a CSV file to upload.');
                return;
            }

            const file = fileInput.files[0];
            const reader = new FileReader();

            reader.onload = function(e) {
                try {
                    const csvText = e.target.result;
                    portfolioData = parseCSV(csvText);

                    // Load any existing sold states from localStorage
                    loadSoldStatesFromStorage();

                    // Process the portfolio data
                    processPortfolioData();

                    // Show success message
                    showSuccess(`Successfully loaded ${portfolioData.length} transactions from portfolio file.`);

                    // Show all sections
                    document.getElementById('summarySection').classList.remove('hidden');
                    document.getElementById('dashboardSection').classList.remove('hidden');
                    document.getElementById('holdingsSection').classList.remove('hidden');
                    document.getElementById('integrationSection').classList.remove('hidden');

                } catch (error) {
                    showError('Error processing CSV file: ' + error.message);
                }
            };

            reader.readAsText(file);
        }

        function processPortfolioData() {
            // Sort transactions by date
            const sorted = portfolioData.sort((a, b) => parseDate(a.Date) - parseDate(b.Date));

            // Calculate current holdings
            const holdings = new Map();

            for (let transaction of sorted) {
                const date = parseDate(transaction.Date);
                const code = transaction.Code;
                const action = transaction.Action.toLowerCase();
                const quantity = parseInt(transaction.Quantity);
                const price = parseFloat(transaction['Avg. price']);
                const fees = parseFloat(transaction.Fees) || 0;
                const settlValue = parseFloat(transaction['Settl. value']);

                if (action === 'buy') {
                    if (!holdings.has(code)) {
                        holdings.set(code, []);
                    }

                    // Check if this transaction is marked as sold
                    const txKey = `${date.getTime()}-${code}-${action}-${quantity}-${price}`;
                    const soldState = getSoldStateFromStorage(txKey);

                    holdings.get(code).push({
                        date: date,
                        quantity: quantity,
                        price: price,
                        fees: fees,
                        settlValue: settlValue,
                        sold: soldState,
                        transactionKey: txKey
                    });
                }
            }

            // Convert to current holdings array
            currentHoldings = [];
            holdings.forEach((transactions, code) => {
                transactions.forEach((tx, index) => {
                    if (!tx.sold) { // Only include non-sold transactions
                        currentHoldings.push({
                            code: code,
                            date: tx.date,
                            quantity: tx.quantity,
                            avgPrice: tx.price,
                            costBase: tx.settlValue,
                            currentValue: tx.quantity * tx.price, // Placeholder - would need real-time prices
                            unrealizedGainLoss: 0, // Placeholder
                            sold: tx.sold,
                            transactionKey: tx.transactionKey,
                            holdingIndex: currentHoldings.length
                        });
                    }
                });
            });

            // Update displays
            updateSummaryCards();
            updateChartsAndTables();
        }

        function updateSummaryCards() {
            let totalValue = 0;
            let totalCostBase = 0;
            let unrealizedGainLoss = 0;

            currentHoldings.forEach(holding => {
                totalValue += holding.currentValue;
                totalCostBase += holding.costBase;
                unrealizedGainLoss += holding.unrealizedGainLoss;
            });

            document.getElementById('totalValue').textContent = `$${totalValue.toFixed(2)}`;
            document.getElementById('totalCostBase').textContent = `$${totalCostBase.toFixed(2)}`;
            document.getElementById('unrealizedGainLoss').textContent = `$${unrealizedGainLoss.toFixed(2)}`;
            document.getElementById('numberOfHoldings').textContent = currentHoldings.length;

            // Color code the unrealized gain/loss card
            const unrealizedCard = document.getElementById('unrealizedGainLoss').closest('.summary-card');
            unrealizedCard.classList.remove('profit-card', 'loss-card', 'neutral-card');

            if (unrealizedGainLoss > 0) {
                unrealizedCard.classList.add('profit-card');
            } else if (unrealizedGainLoss < 0) {
                unrealizedCard.classList.add('loss-card');
            } else {
                unrealizedCard.classList.add('neutral-card');
            }
        }

        function updateChartsAndTables() {
            updateAllocationChart();
            updateGainsChart();
            updateHoldingsTable();
        }

        function updateAllocationChart() {
            const ctx = document.getElementById('allocationChart').getContext('2d');

            // Group holdings by stock code
            const allocation = new Map();
            currentHoldings.forEach(holding => {
                if (allocation.has(holding.code)) {
                    allocation.set(holding.code, allocation.get(holding.code) + holding.currentValue);
                } else {
                    allocation.set(holding.code, holding.currentValue);
                }
            });

            const labels = Array.from(allocation.keys());
            const data = Array.from(allocation.values());
            const colors = generateColors(labels.length);

            if (allocationChart) {
                allocationChart.destroy();
            }

            allocationChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: colors,
                        borderColor: '#1a1a2e',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#e0e0e0',
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return `${context.label}: $${value.toFixed(2)} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        function updateGainsChart() {
            const ctx = document.getElementById('gainsChart').getContext('2d');

            // Group gains by stock code
            const gains = new Map();
            currentHoldings.forEach(holding => {
                if (gains.has(holding.code)) {
                    gains.set(holding.code, gains.get(holding.code) + holding.unrealizedGainLoss);
                } else {
                    gains.set(holding.code, holding.unrealizedGainLoss);
                }
            });

            const labels = Array.from(gains.keys());
            const data = Array.from(gains.values());
            const colors = data.map(value => value >= 0 ? '#4CAF50' : '#f44336');

            if (gainsChart) {
                gainsChart.destroy();
            }

            gainsChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Unrealized Gain/Loss',
                        data: data,
                        backgroundColor: colors,
                        borderColor: colors,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: '#e0e0e0',
                                callback: function(value) {
                                    return '$' + value.toFixed(0);
                                }
                            },
                            grid: {
                                color: '#555'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#e0e0e0'
                            },
                            grid: {
                                color: '#555'
                            }
                        }
                    }
                }
            });
        }

        function updateHoldingsTable() {
            const tbody = document.getElementById('holdingsBody');
            tbody.innerHTML = '';

            currentHoldings.forEach((holding, index) => {
                const row = tbody.insertRow();
                if (holding.sold) {
                    row.classList.add('sold-transaction');
                }

                const gainLossClass = holding.unrealizedGainLoss >= 0 ? 'gain' : 'loss';
                const gainLossIcon = holding.unrealizedGainLoss >= 0 ? '📈 +' : '📉 ';

                row.innerHTML = `
                    <td>${holding.code}</td>
                    <td>${holding.date.toLocaleDateString('en-AU')}</td>
                    <td>${holding.quantity.toLocaleString()}</td>
                    <td>$${holding.avgPrice.toFixed(3)}</td>
                    <td>$${holding.costBase.toFixed(2)}</td>
                    <td>$${holding.currentValue.toFixed(2)}</td>
                    <td class="${gainLossClass}">
                        ${gainLossIcon}$${Math.abs(holding.unrealizedGainLoss).toFixed(2)}
                    </td>
                    <td>
                        <input type="checkbox" class="sold-checkbox" ${holding.sold ? 'checked' : ''}
                            onchange="toggleSoldStatus(${index})" title="Mark as already sold">
                    </td>
                    <td>
                        <button onclick="viewTransactionDetails(${index})" style="padding: 5px 10px; font-size: 12px;">
                            📋 Details
                        </button>
                    </td>
                `;
            });
        }

        // Utility functions
        function generateColors(count) {
            const colors = [
                '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336',
                '#00BCD4', '#8BC34A', '#FF5722', '#795548', '#607D8B',
                '#E91E63', '#FFC107', '#3F51B5', '#009688', '#CDDC39'
            ];

            const result = [];
            for (let i = 0; i < count; i++) {
                result.push(colors[i % colors.length]);
            }
            return result;
        }

        function toggleSoldStatus(holdingIndex) {
            if (currentHoldings[holdingIndex]) {
                currentHoldings[holdingIndex].sold = !currentHoldings[holdingIndex].sold;

                // Save to localStorage
                saveSoldStateToStorage(
                    currentHoldings[holdingIndex].transactionKey,
                    currentHoldings[holdingIndex].sold
                );

                // Update displays
                updateHoldingsTable();
                updateSummaryCards();
                updateChartsAndTables();

                // Update integration status
                updateIntegrationStatus();
            }
        }

        function viewTransactionDetails(holdingIndex) {
            const holding = currentHoldings[holdingIndex];
            alert(`Transaction Details:

Stock Code: ${holding.code}
Date: ${holding.date.toLocaleDateString('en-AU')}
Quantity: ${holding.quantity.toLocaleString()}
Average Price: $${holding.avgPrice.toFixed(3)}
Cost Base: $${holding.costBase.toFixed(2)}
Current Value: $${holding.currentValue.toFixed(2)}
Unrealized Gain/Loss: $${holding.unrealizedGainLoss.toFixed(2)}
Sold Status: ${holding.sold ? 'Marked as Sold' : 'Available'}
            `);
        }

        // LocalStorage functions for sold states
        function saveSoldStateToStorage(transactionKey, soldState) {
            const soldStates = JSON.parse(localStorage.getItem('portfolioSoldStates') || '{}');
            soldStates[transactionKey] = soldState;
            localStorage.setItem('portfolioSoldStates', JSON.stringify(soldStates));
        }

        function getSoldStateFromStorage(transactionKey) {
            const soldStates = JSON.parse(localStorage.getItem('portfolioSoldStates') || '{}');
            return soldStates[transactionKey] || false;
        }

        function loadSoldStatesFromStorage() {
            // This will be called when loading portfolio data to restore sold states
            const soldStates = JSON.parse(localStorage.getItem('portfolioSoldStates') || '{}');
            return soldStates;
        }

        // CGT Calculator Integration functions
        function syncWithCGTCalculator() {
            try {
                // Get data from CGT calculator if available
                const cgtData = localStorage.getItem('cgtCalculatorData');
                if (cgtData) {
                    const parsedData = JSON.parse(cgtData);
                    showSuccess('Successfully synced with CGT Calculator data.');
                    updateIntegrationStatus();
                } else {
                    showError('No CGT Calculator data found. Please run the CGT Calculator first.');
                }
            } catch (error) {
                showError('Error syncing with CGT Calculator: ' + error.message);
            }
        }

        function exportToCGTCalculator() {
            try {
                // Prepare portfolio data for CGT calculator
                const exportData = {
                    portfolioData: portfolioData,
                    soldStates: JSON.parse(localStorage.getItem('portfolioSoldStates') || '{}'),
                    timestamp: new Date().toISOString()
                };

                localStorage.setItem('portfolioExportData', JSON.stringify(exportData));
                showSuccess('Portfolio data exported successfully. You can now import it in the CGT Calculator.');

                // Optionally open CGT calculator
                if (confirm('Would you like to open the CGT Calculator now?')) {
                    window.open('cgt-calculator.html', '_blank');
                }
            } catch (error) {
                showError('Error exporting to CGT Calculator: ' + error.message);
            }
        }

        function importFromCGTCalculator() {
            try {
                const cgtData = localStorage.getItem('cgtCalculatorData');
                if (cgtData) {
                    const parsedData = JSON.parse(cgtData);

                    // Import sold states from CGT calculator
                    if (parsedData.soldStates) {
                        localStorage.setItem('portfolioSoldStates', JSON.stringify(parsedData.soldStates));

                        // Reload portfolio data with new sold states
                        if (portfolioData.length > 0) {
                            processPortfolioData();
                        }

                        showSuccess('Successfully imported sold states from CGT Calculator.');
                    } else {
                        showError('No sold state data found in CGT Calculator.');
                    }
                } else {
                    showError('No CGT Calculator data found.');
                }
            } catch (error) {
                showError('Error importing from CGT Calculator: ' + error.message);
            }
        }

        function updateIntegrationStatus() {
            const statusDiv = document.getElementById('integrationStatus');
            const soldCount = currentHoldings.filter(h => h.sold).length;
            const totalCount = currentHoldings.length;

            statusDiv.innerHTML = `
                <div style="background: rgba(33, 150, 243, 0.1); padding: 15px; border-radius: 8px; border: 1px solid #2196F3;">
                    <h4 style="color: #2196F3; margin-bottom: 10px;">📊 Integration Status</h4>
                    <p><strong>Total Holdings:</strong> ${totalCount}</p>
                    <p><strong>Marked as Sold:</strong> ${soldCount}</p>
                    <p><strong>Available for CGT:</strong> ${totalCount - soldCount}</p>
                    <p style="margin-top: 10px; color: #4CAF50;">
                        ✅ Portfolio data is ready for CGT Calculator integration
                    </p>
                </div>
            `;
        }

        function generatePortfolioReport() {
            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                // Add title
                doc.setFontSize(20);
                doc.text('Portfolio Management Report', 20, 20);

                // Add summary
                doc.setFontSize(12);
                doc.text(`Generated: ${new Date().toLocaleDateString('en-AU')}`, 20, 35);
                doc.text(`Total Holdings: ${currentHoldings.length}`, 20, 45);

                const totalValue = currentHoldings.reduce((sum, h) => sum + h.currentValue, 0);
                const totalCostBase = currentHoldings.reduce((sum, h) => sum + h.costBase, 0);
                const unrealizedGainLoss = totalValue - totalCostBase;

                doc.text(`Total Portfolio Value: $${totalValue.toFixed(2)}`, 20, 55);
                doc.text(`Total Cost Base: $${totalCostBase.toFixed(2)}`, 20, 65);
                doc.text(`Unrealized Gain/Loss: $${unrealizedGainLoss.toFixed(2)}`, 20, 75);

                // Add holdings table
                const tableData = currentHoldings.map(holding => [
                    holding.code,
                    holding.date.toLocaleDateString('en-AU'),
                    holding.quantity.toString(),
                    `$${holding.avgPrice.toFixed(3)}`,
                    `$${holding.costBase.toFixed(2)}`,
                    `$${holding.currentValue.toFixed(2)}`,
                    `$${holding.unrealizedGainLoss.toFixed(2)}`,
                    holding.sold ? 'Yes' : 'No'
                ]);

                doc.autoTable({
                    head: [['Code', 'Date', 'Quantity', 'Avg Price', 'Cost Base', 'Current Value', 'Unrealized G/L', 'Sold']],
                    body: tableData,
                    startY: 90,
                    styles: { fontSize: 8 }
                });

                // Save the PDF
                doc.save('portfolio-report.pdf');
                showSuccess('Portfolio report generated successfully.');

            } catch (error) {
                showError('Error generating portfolio report: ' + error.message);
            }
        }

        // Utility functions for messages
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            document.querySelector('.container').insertBefore(errorDiv, document.querySelector('.container').firstChild.nextSibling);

            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            document.querySelector('.container').insertBefore(successDiv, document.querySelector('.container').firstChild.nextSibling);

            setTimeout(() => {
                successDiv.remove();
            }, 5000);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Check if there's any existing portfolio data in localStorage
            const existingData = localStorage.getItem('portfolioExportData');
            if (existingData) {
                updateIntegrationStatus();
            }
        });
    </script>
</body>
</html>
