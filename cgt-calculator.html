<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Australian CGT Calculator & Portfolio Tracker</title>
    <!-- PDF Generation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #e0e0e0;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(30, 30, 50, 0.9);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        h1 {
            text-align: center;
            color: #4CAF50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .setup-section {
            background: rgba(40, 40, 60, 0.8);
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
            border: 1px solid #4CAF50;
        }

        .section-title {
            color: #4CAF50;
            font-size: 1.4em;
            margin-bottom: 15px;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 5px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #b0b0b0;
            font-weight: 500;
        }

        input[type="number"], input[type="file"], select {
            width: 100%;
            padding: 12px;
            background: rgba(60, 60, 80, 0.8);
            border: 1px solid #555;
            border-radius: 8px;
            color: #e0e0e0;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="number"]:focus, input[type="file"]:focus, select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
        }

        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        button:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .settings-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .results-section {
            background: rgba(40, 40, 60, 0.8);
            padding: 25px;
            border-radius: 10px;
            margin-top: 25px;
            border: 1px solid #2196F3;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .summary-card {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            color: white;
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
        }

        .summary-card h3 {
            font-size: 0.9em;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .summary-card .value {
            font-size: 1.8em;
            font-weight: bold;
        }

        .transactions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(50, 50, 70, 0.8);
            border-radius: 8px;
            overflow: hidden;
        }

        .transactions-table th,
        .transactions-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #555;
        }

        .transactions-table th {
            background: rgba(76, 175, 80, 0.8);
            color: white;
            font-weight: 600;
        }

        .transactions-table tr:hover {
            background: rgba(76, 175, 80, 0.1);
        }

        .buy-row {
            background: rgba(76, 175, 80, 0.1);
        }

        .sell-row {
            background: rgba(244, 67, 54, 0.1);
        }

        .gain {
            color: #4CAF50;
            font-weight: bold;
        }

        .loss {
            color: #f44336;
            font-weight: bold;
        }

        .profit-card {
            background: linear-gradient(135deg, #4CAF50, #45a049) !important;
        }

        .loss-card {
            background: linear-gradient(135deg, #f44336, #d32f2f) !important;
        }

        .neutral-card {
            background: linear-gradient(135deg, #757575, #616161) !important;
        }

        .fifo-match-1 { background: rgba(255, 193, 7, 0.2) !important; border-left: 4px solid #FFC107; }
        .fifo-match-2 { background: rgba(156, 39, 176, 0.2) !important; border-left: 4px solid #9C27B0; }
        .fifo-match-3 { background: rgba(255, 87, 34, 0.2) !important; border-left: 4px solid #FF5722; }
        .fifo-match-4 { background: rgba(0, 188, 212, 0.2) !important; border-left: 4px solid #00BCD4; }
        .fifo-match-5 { background: rgba(139, 195, 74, 0.2) !important; border-left: 4px solid #8BC34A; }
        .fifo-match-6 { background: rgba(121, 85, 72, 0.2) !important; border-left: 4px solid #795548; }
        .fifo-match-7 { background: rgba(96, 125, 139, 0.2) !important; border-left: 4px solid #607D8B; }
        .fifo-match-8 { background: rgba(233, 30, 99, 0.2) !important; border-left: 4px solid #E91E63; }

        .sold-transaction {
            background: rgba(128, 128, 128, 0.3) !important;
            opacity: 0.7;
            position: relative;
        }

        .sold-transaction::after {
            content: "SOLD";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(244, 67, 54, 0.8);
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.7em;
            font-weight: bold;
            pointer-events: none;
            z-index: 1;
        }

        .sold-checkbox {
            transform: scale(1.2);
            cursor: pointer;
        }

        .sold-checkbox:checked {
            accent-color: #f44336;
        }

        .ato-section {
            background: rgba(40, 40, 60, 0.8);
            padding: 25px;
            border-radius: 10px;
            margin-top: 25px;
            border: 1px solid #FF9800;
        }

        .ato-item {
            background: rgba(50, 50, 70, 0.8);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #FF9800;
        }

        .copy-button {
            background: linear-gradient(45deg, #FF9800, #F57C00);
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-left: 10px;
            transition: all 0.3s;
        }

        .copy-button:hover {
            background: linear-gradient(45deg, #F57C00, #FF9800);
            transform: translateY(-1px);
        }

        .fifo-legend {
            background: rgba(50, 50, 70, 0.8);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #4CAF50;
        }

        .legend-item {
            padding: 12px;
            margin: 8px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }

        .legend-item {
            display: inline-block;
            margin: 5px 10px;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .hidden {
            display: none;
        }

        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #ffcdd2;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #c8e6c9;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .settings-panel {
                grid-template-columns: 1fr;
            }
            
            .summary-cards {
                grid-template-columns: 1fr;
            }
            
            .transactions-table {
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Bradley's Automatic CGT Calculator!</h1>

        <!-- Navigation -->
        <div style="background: rgba(40, 40, 60, 0.8); padding: 15px; border-radius: 10px; margin-bottom: 25px; border: 1px solid #2196F3; text-align: center;">
            <a href="cgt-calculator.html" style="display: inline-block; background: linear-gradient(45deg, #4CAF50, #45a049); color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 0 10px; transition: all 0.3s; font-weight: 600;">📈 CGT Calculator</a>
            <a href="ato-tax-calculator.html" style="display: inline-block; background: linear-gradient(45deg, #2196F3, #1976D2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; margin: 0 10px; transition: all 0.3s; font-weight: 600;">🧮 Tax Return Calculator</a>
        </div>
        
        <!-- Initial Setup Section -->
        <div class="setup-section">
            <h2 class="section-title">Initial Setup</h2>
            
            <div class="input-group">
                <label for="annualSalary">Annual Salary (AUD):</label>
                <input type="number" id="annualSalary" placeholder="e.g., 75000" min="0" step="1000">
            </div>
            
            <div class="input-group">
                <label for="csvFile">Upload CSV File:</label>
                <input type="file" id="csvFile" accept=".csv" />
                <small style="color: #888; display: block; margin-top: 5px;">
                    Expected columns: Date, Confirmation No., Code, Quantity, Action, Avg. price, Fees, Settl. value
                </small>
            </div>

            <div class="input-group">
                <label for="matchingMethod">Paper Matching Method:</label>
                <select id="matchingMethod">
                    <option value="fifo">FIFO (First-In-First-Out) - Chronological</option>
                    <option value="hifo">HIFO (Highest-In-First-Out) - Tax Optimized</option>
                    <option value="lofo">LOFO (Lowest-In-First-Out) - Highest Gains</option>
                    <option value="lifo">LIFO (Last-In-First-Out) - Newest First</option>
                </select>
                <small style="color: #888; display: block; margin-top: 5px;">
                    HIFO minimizes capital gains tax by selling highest-cost shares first
                </small>
            </div>

            <button onclick="processData()">Calculate CGT</button>
            <button onclick="showSettings()">Settings</button>
        </div>

        <!-- Settings Panel -->
        <div id="settingsPanel" class="setup-section hidden">
            <h2 class="section-title">Tax Settings</h2>
            <div class="settings-panel">
                <div class="input-group">
                    <label for="cgtDiscount">CGT Discount Rate (%):</label>
                    <input type="number" id="cgtDiscount" value="50" min="0" max="100" step="1">
                    <small style="color: #888;">Default: 50% for assets held >12 months</small>
                </div>
                
                <div class="input-group">
                    <label for="medicareLevy">Medicare Levy (%):</label>
                    <input type="number" id="medicareLevy" value="2" min="0" max="10" step="0.1">
                    <small style="color: #888;">Default: 2%</small>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="results-section hidden">
            <h2 class="section-title">CGT Calculation Results</h2>
            
            <div class="summary-cards">
                <div class="summary-card">
                    <h3>Total Capital Gains</h3>
                    <div class="value" id="totalGains">$0.00</div>
                </div>
                <div class="summary-card">
                    <h3>Total Capital Losses</h3>
                    <div class="value" id="totalLosses">$0.00</div>
                </div>
                <div class="summary-card">
                    <h3>Net Capital Gain</h3>
                    <div class="value" id="netGain">$0.00</div>
                </div>
                <div class="summary-card">
                    <h3>Estimated Tax Liability</h3>
                    <div class="value" id="taxLiability">$0.00</div>
                </div>
            </div>
            
            <div id="taxBracketInfo" style="margin: 20px 0; padding: 15px; background: rgba(33, 150, 243, 0.1); border-radius: 8px; border: 1px solid #2196F3;">
                <h4 style="color: #2196F3; margin-bottom: 10px;">Your Tax Bracket Information</h4>
                <div id="taxBracketDetails"></div>
            </div>
            
            <div class="fifo-legend">
                <h4 style="color: #4CAF50; margin-bottom: 10px;">🔗 <span id="matchingMethodTitle">FIFO</span> Matching Details</h4>
                <p style="margin-bottom: 15px; color: #b0b0b0;">
                    <span id="matchingMethodDescription">Each colored section shows how sales are matched to purchases using First-In-First-Out (FIFO) method.</span>
                    Partial disposals show the percentage used and remaining quantities.
                </p>
                <p style="margin-bottom: 15px; color: #FFC107; font-size: 0.9em;">
                    💡 <strong>Tip:</strong> Use the "Sold ✓" checkbox on buy transactions to mark shares as already sold outside this dataset.
                    Checked transactions will be excluded from matching and appear grayed out.
                </p>
                <div id="fifoLegendItems"></div>
            </div>

            <h3 style="margin-top: 30px; color: #4CAF50;">Transaction Details</h3>
            <table class="transactions-table" id="transactionsTable">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Code</th>
                        <th>Action</th>
                        <th>Quantity</th>
                        <th>Remaining</th>
                        <th title="Check to mark buy transactions as already sold (excludes from matching)">Sold ✓</th>
                        <th>Price</th>
                        <th>Fees</th>
                        <th>Total Value</th>
                        <th>Matching Details</th>
                        <th>Capital Gain/Loss</th>
                        <th>CGT Discount</th>
                        <th>Holding Period</th>
                    </tr>
                </thead>
                <tbody id="transactionsBody">
                </tbody>
            </table>
        </div>

        <!-- Auto-Prefilled Questions Section -->
        <div id="autoPrefilledSection" class="ato-section hidden">
            <h2 class="section-title" style="color: #4CAF50;">📋 Auto-Prefilled Tax Questions</h2>
            <p style="color: #b0b0b0; margin-bottom: 20px;">These questions are automatically filled based on your CGT calculations:</p>

            <!-- Income Year Selection -->
            <div class="ato-item">
                <h4 style="color: #4CAF50;">Select an income year *</h4>
                <p><strong>Recommended Year:</strong> <span id="autoIncomeYear" style="font-weight: bold; color: #4CAF50;">2024-25</span></p>
                <button class="copy-button" onclick="copyToClipboard('autoIncomeYear')">📋 Copy</button>
                <small style="color: #888;">Based on your transaction dates</small>
            </div>

            <!-- Net Capital Losses from Previous Years -->
            <div class="ato-item">
                <h4 style="color: #4CAF50;">Do you have any unapplied net capital losses from previous years? *</h4>
                <div style="margin: 10px 0;">
                    <label style="display: flex; align-items: center; margin: 5px 0; cursor: pointer;">
                        <input type="radio" name="previousLosses" value="no" checked style="margin-right: 10px;">
                        <span style="color: #4CAF50; font-weight: bold;">No</span>
                    </label>
                    <label style="display: flex; align-items: center; margin: 5px 0; cursor: pointer;">
                        <input type="radio" name="previousLosses" value="yes" style="margin-right: 10px;">
                        <span>Yes</span>
                    </label>
                </div>
                <small style="color: #888;">Default: No (you can change this if you have previous losses)</small>
            </div>

            <!-- Collectables Losses from Previous Years -->
            <div class="ato-item">
                <h4 style="color: #4CAF50;">Do you have any unapplied losses from previous years for collectables? *</h4>
                <div style="margin: 10px 0;">
                    <label style="display: flex; align-items: center; margin: 5px 0; cursor: pointer;">
                        <input type="radio" name="collectablesLosses" value="no" checked style="margin-right: 10px;">
                        <span style="color: #4CAF50; font-weight: bold;">No</span>
                    </label>
                    <label style="display: flex; align-items: center; margin: 5px 0; cursor: pointer;">
                        <input type="radio" name="collectablesLosses" value="yes" style="margin-right: 10px;">
                        <span>Yes</span>
                    </label>
                </div>
                <small style="color: #888;">Default: No (shares are not collectables)</small>
            </div>
        </div>

        <!-- Individual Asset Details Section -->
        <div id="assetDetailsSection" class="ato-section hidden">
            <h2 class="section-title" style="color: #2196F3;">📊 Individual Asset Details for Each Sale</h2>
            <p style="color: #b0b0b0; margin-bottom: 20px;">Auto-filled details for each share sale transaction:</p>
            <div id="assetDetailsList"></div>
        </div>

        <!-- ATO Summary Section -->
        <div id="atoSection" class="ato-section hidden">
            <h2 class="section-title" style="color: #FF9800;">🏛️ ATO Tax Return Summary - Item 18 Capital Gains</h2>
            <p style="color: #b0b0b0; margin-bottom: 20px;">Complete Item 18 in your Australian tax return supplementary section using these values:</p>

            <!-- Label G: CGT Event Yes/No -->
            <div class="ato-item">
                <h4 style="color: #FF9800;">Item 18 - Label G: Did you have a capital gains tax event during the year?</h4>
                <p><strong>Answer:</strong> <span id="atoLabelG" style="font-weight: bold; color: #4CAF50;">YES</span></p>
                <small style="color: #888;">Print X in the YES box at Item 18 - Label G</small>
            </div>

            <!-- Label H: Total Current Year Capital Gains -->
            <div class="ato-item">
                <h4 style="color: #FF9800;">Item 18 - Label H: Total current year capital gains</h4>
                <p><strong>Amount (before applying losses and discounts):</strong>
                    <span id="atoLabelH">$0.00</span>
                    <button class="copy-button" onclick="copyToClipboard('atoLabelH')">📋 Copy</button>
                </p>
                <small style="color: #888;">Enter this amount at Item 18 - Label H in your tax return</small>
            </div>

            <!-- Label A: Net Capital Gain -->
            <div class="ato-item">
                <h4 style="color: #FF9800;">Item 18 - Label A: Net capital gain</h4>
                <p><strong>Amount (after applying losses and CGT discount):</strong>
                    <span id="atoLabelA">$0.00</span>
                    <button class="copy-button" onclick="copyToClipboard('atoLabelA')">📋 Copy</button>
                </p>
                <small style="color: #888;">Enter this amount at Item 18 - Label A in your tax return</small>
            </div>

            <!-- Label V: Net Capital Losses Carried Forward -->
            <div class="ato-item" id="atoLabelVSection" style="display: none;">
                <h4 style="color: #FF9800;">Item 18 - Label V: Net capital losses carried forward</h4>
                <p><strong>Amount to carry forward to later income years:</strong>
                    <span id="atoLabelV">$0.00</span>
                    <button class="copy-button" onclick="copyToClipboard('atoLabelV')">📋 Copy</button>
                </p>
                <small style="color: #888;">Enter this amount at Item 18 - Label V in your tax return</small>
            </div>

            <!-- Label M: Exemptions/Rollovers -->
            <div class="ato-item">
                <h4 style="color: #FF9800;">Item 18 - Label M: Have you applied an exemption or rollover?</h4>
                <p><strong>Answer:</strong> <span id="atoLabelM" style="font-weight: bold;">NO</span></p>
                <p id="atoLabelMCode" style="display: none;"><strong>Code:</strong> <span id="atoExemptionCode"></span></p>
                <small style="color: #888;">Print X in the appropriate box at Item 18 - Label M</small>
            </div>

            <!-- Additional Information -->
            <div class="ato-item">
                <h4 style="color: #FF9800;">Additional CGT Information</h4>
                <p><strong>Total CGT discount applied:</strong>
                    <span id="atoCgtDiscount">$0.00</span>
                    <button class="copy-button" onclick="copyToClipboard('atoCgtDiscount')">📋 Copy</button>
                </p>
                <small style="color: #888;">For your records - this discount is already included in the net capital gain calculation</small>
            </div>

            <div class="ato-item" style="border-left-color: #4CAF50; background: rgba(76, 175, 80, 0.1);">
                <h4 style="color: #4CAF50;">💡 Official ATO Tax Return Instructions</h4>
                <ul style="margin: 10px 0; padding-left: 20px; color: #b0b0b0;">
                    <li>Complete <strong>Item 18 Capital gains</strong> in the supplementary section of your tax return</li>
                    <li>Print <strong>X</strong> in the YES box at <strong>Label G</strong> if you had a CGT event</li>
                    <li>Enter total current year capital gains at <strong>Label H</strong></li>
                    <li>Enter net capital gain at <strong>Label A</strong> (or net capital losses at <strong>Label V</strong>)</li>
                    <li>Print <strong>X</strong> at <strong>Label M</strong> if you applied any exemptions or rollovers</li>
                    <li>Keep detailed records for 5 years after disposal as required by ATO</li>
                    <li>Refer to ATO Guide to capital gains tax 2024 for detailed instructions</li>
                    <li>Consider consulting a tax professional for complex situations</li>
                </ul>
            </div>

            <div class="ato-item" style="border-left-color: #2196F3; background: rgba(33, 150, 243, 0.1); text-align: center;">
                <h4 style="color: #2196F3;">📄 Professional ATO Report</h4>
                <p style="color: #b0b0b0; margin: 10px 0;">Generate a comprehensive PDF report that meets ATO record-keeping requirements</p>
                <button class="copy-button" onclick="generateATOReport({ totalGains, totalLosses, netCapitalGain, totalCgtDiscount })" style="background: linear-gradient(45deg, #2196F3, #1976D2); font-size: 16px; padding: 12px 25px;">
                    📄 Generate ATO PDF Report
                </button>
                <p style="color: #888; font-size: 12px; margin-top: 10px;">
                    Includes all transaction details, cost basis calculations, and ATO compliance information
                </p>
            </div>
        </div>
    </div>

    <script>
        // Australian Tax Brackets 2024-25
        const TAX_BRACKETS_2024_25 = [
            { min: 0, max: 18200, rate: 0 },
            { min: 18201, max: 45000, rate: 0.16, base: 0 },
            { min: 45001, max: 135000, rate: 0.30, base: 4288 },
            { min: 135001, max: 190000, rate: 0.37, base: 31288 },
            { min: 190001, max: Infinity, rate: 0.45, base: 51638 }
        ];

        let processedTransactions = [];

        function showSettings() {
            const panel = document.getElementById('settingsPanel');
            panel.classList.toggle('hidden');
        }

        function toggleSoldStatus(transactionIndex) {
            if (processedTransactions[transactionIndex]) {
                processedTransactions[transactionIndex].sold = !processedTransactions[transactionIndex].sold;

                // Recalculate and redisplay results
                const processedData = {
                    transactions: processedTransactions,
                    matches: [], // Will be recalculated
                    method: document.getElementById('matchingMethod').value
                };

                // Reprocess transactions to update matching
                reprocessTransactions();
            }
        }

        function reprocessTransactions() {
            // Get the original CSV data and reprocess it with the current sold states
            const csvFile = document.getElementById('csvFile');
            if (csvFile.files.length > 0) {
                const file = csvFile.files[0];
                const reader = new FileReader();
                reader.onload = function(e) {
                    const csvText = e.target.result;
                    const rawTransactions = parseCSV(csvText);

                    // Apply the current sold states to the raw transactions
                    const processedData = processTransactions(rawTransactions, true);
                    displayResults(processedData);
                };
                reader.readAsText(file);
            }
        }

        function parseCSV(csvText) {
            const lines = csvText.trim().split('\n');
            const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
            
            const data = [];
            for (let i = 1; i < lines.length; i++) {
                const values = parseCSVLine(lines[i]);
                if (values.length === headers.length) {
                    const row = {};
                    headers.forEach((header, index) => {
                        row[header] = values[index].trim().replace(/"/g, '');
                    });
                    data.push(row);
                }
            }
            return data;
        }

        function parseCSVLine(line) {
            const result = [];
            let current = '';
            let inQuotes = false;
            
            for (let i = 0; i < line.length; i++) {
                const char = line[i];
                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    result.push(current);
                    current = '';
                } else {
                    current += char;
                }
            }
            result.push(current);
            return result;
        }

        function parseDate(dateStr) {
            // Handle various date formats
            const formats = [
                /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/, // DD/MM/YYYY or D/M/YYYY
                /^(\d{4})-(\d{1,2})-(\d{1,2})$/, // YYYY-MM-DD
            ];
            
            for (let format of formats) {
                const match = dateStr.match(format);
                if (match) {
                    if (format === formats[0]) {
                        // DD/MM/YYYY format
                        return new Date(match[3], match[2] - 1, match[1]);
                    } else {
                        // YYYY-MM-DD format
                        return new Date(match[1], match[2] - 1, match[3]);
                    }
                }
            }
            
            // Fallback to Date constructor
            return new Date(dateStr);
        }

        function calculateTax(income) {
            const medicareLevy = parseFloat(document.getElementById('medicareLevy').value) / 100;
            let tax = 0;
            
            for (let bracket of TAX_BRACKETS_2024_25) {
                if (income > bracket.min) {
                    const taxableInBracket = Math.min(income, bracket.max) - bracket.min + 1;
                    tax = bracket.base + (taxableInBracket * bracket.rate);
                }
            }
            
            // Add Medicare Levy
            if (income > 23226) { // Medicare levy threshold 2024-25
                tax += income * medicareLevy;
            }
            
            return tax;
        }

        function getTaxBracket(income) {
            for (let bracket of TAX_BRACKETS_2024_25) {
                if (income >= bracket.min && income <= bracket.max) {
                    return bracket;
                }
            }
            return TAX_BRACKETS_2024_25[TAX_BRACKETS_2024_25.length - 1];
        }

        function calculateHoldingPeriod(buyDate, sellDate) {
            const diffTime = sellDate - buyDate;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return diffDays;
        }

        function sortBuyTransactionsForMatching(buyTransactions, method) {
            // Create a copy to avoid modifying the original array
            const sorted = [...buyTransactions];

            switch(method) {
                case 'fifo':
                    // Sort by date (oldest first) - already sorted by date in main function
                    return sorted.sort((a, b) => a.date - b.date);
                case 'hifo':
                    // Sort by price (highest first)
                    return sorted.sort((a, b) => b.price - a.price);
                case 'lofo':
                    // Sort by price (lowest first)
                    return sorted.sort((a, b) => a.price - b.price);
                case 'lifo':
                    // Sort by date (newest first)
                    return sorted.sort((a, b) => b.date - a.date);
                default:
                    return sorted;
            }
        }

        function processTransactions(rawTransactions, preserveSoldStates = false) {
            // Sort transactions by date
            const sorted = rawTransactions.sort((a, b) => parseDate(a.Date) - parseDate(b.Date));

            // Get selected matching method
            const matchingMethod = document.getElementById('matchingMethod').value;

            // Create a map of existing sold states if preserving
            const soldStatesMap = new Map();
            if (preserveSoldStates && processedTransactions.length > 0) {
                processedTransactions.forEach((tx, index) => {
                    if (tx.action === 'buy') {
                        const key = `${tx.date.getTime()}-${tx.code}-${tx.action}-${tx.quantity}-${tx.price}`;
                        soldStatesMap.set(key, tx.sold);
                    }
                });
            }

            const processed = [];
            const holdings = new Map(); // code -> array of buy transactions
            const matches = []; // Track matches for highlighting
            let matchColorIndex = 0;

            for (let transaction of sorted) {
                const date = parseDate(transaction.Date);
                const code = transaction.Code;
                const action = transaction.Action.toLowerCase();
                const quantity = parseInt(transaction.Quantity);
                const price = parseFloat(transaction['Avg. price']);
                const fees = parseFloat(transaction.Fees) || 0;
                const settlValue = parseFloat(transaction['Settl. value']);
                
                // Check if we should preserve the sold state for this transaction
                const txKey = `${date.getTime()}-${code}-${action}-${quantity}-${price}`;
                const preservedSoldState = soldStatesMap.get(txKey) || false;

                const processedTx = {
                    date: date,
                    code: code,
                    action: action,
                    quantity: quantity,
                    remainingQuantity: quantity, // Track remaining quantity for buys
                    price: price,
                    fees: fees,
                    settlValue: settlValue,
                    capitalGain: 0,
                    cgtDiscount: 0,
                    holdingPeriod: 0,
                    discountApplied: false,
                    fifoMatchId: null,
                    matchedTransactions: [],
                    fifoDetails: null, // Will store detailed FIFO matching info
                    sold: action === 'buy' ? preservedSoldState : false // Track if this buy transaction is marked as already sold
                };
                
                if (action === 'buy') {
                    // Add to holdings
                    if (!holdings.has(code)) {
                        holdings.set(code, []);
                    }
                    holdings.get(code).push({
                        date: date,
                        quantity: quantity,
                        price: price,
                        fees: fees,
                        remainingQuantity: quantity,
                        transactionIndex: processed.length,
                        sold: processedTx.sold // Use the sold state from the processed transaction
                    });
                } else if (action === 'sell') {
                    // Process sell using selected matching method
                    let remainingToSell = quantity;
                    let totalCostBase = 0;
                    let totalFees = fees;
                    let oldestBuyDate = null;
                    const currentMatchId = `match-${matchColorIndex % 8 + 1}`;
                    const matchedBuyTransactions = [];

                    if (holdings.has(code)) {
                        const buyTransactions = holdings.get(code);
                        // Sort buy transactions based on matching method, excluding sold transactions
                        const sortedBuyTransactions = sortBuyTransactionsForMatching(
                            buyTransactions.filter(tx => tx.remainingQuantity > 0 && !tx.sold),
                            matchingMethod
                        );

                        for (let i = 0; i < sortedBuyTransactions.length && remainingToSell > 0; i++) {
                            const buyTx = sortedBuyTransactions[i];
                            if (buyTx.remainingQuantity > 0) {
                                if (!oldestBuyDate) oldestBuyDate = buyTx.date;

                                const quantityToUse = Math.min(remainingToSell, buyTx.remainingQuantity);
                                const costForThisQuantity = quantityToUse * buyTx.price;
                                const feesForThisQuantity = (buyTx.fees * quantityToUse) / buyTx.quantity;

                                totalCostBase += costForThisQuantity + feesForThisQuantity;
                                buyTx.remainingQuantity -= quantityToUse;
                                remainingToSell -= quantityToUse;

                                // Track FIFO matching
                                matchedBuyTransactions.push({
                                    index: buyTx.transactionIndex,
                                    quantity: quantityToUse,
                                    totalQuantity: buyTx.quantity,
                                    remainingAfter: buyTx.remainingQuantity,
                                    buyDate: buyTx.date,
                                    buyPrice: buyTx.price,
                                    fees: feesForThisQuantity // Include the allocated fees for this portion
                                });

                                // Mark the buy transaction with the same match ID and update remaining quantity
                                if (processed[buyTx.transactionIndex]) {
                                    processed[buyTx.transactionIndex].fifoMatchId = currentMatchId;
                                    processed[buyTx.transactionIndex].remainingQuantity = buyTx.remainingQuantity;
                                }
                            }
                        }
                    }

                    processedTx.fifoMatchId = currentMatchId;
                    processedTx.matchedTransactions = matchedBuyTransactions;

                    // Create detailed FIFO information for the sell transaction
                    if (matchedBuyTransactions.length > 0) {
                        processedTx.fifoDetails = matchedBuyTransactions.map(match => {
                            const percentageUsed = ((match.quantity / match.totalQuantity) * 100).toFixed(1);
                            return `${match.quantity.toLocaleString()} from ${match.buyDate.toLocaleDateString('en-AU')} (${percentageUsed}% of ${match.totalQuantity.toLocaleString()})`;
                        }).join('<br>');

                        matches.push({
                            matchId: currentMatchId,
                            sellTransaction: processed.length,
                            buyTransactions: matchedBuyTransactions,
                            code: code,
                            sellQuantity: quantity,
                            sellDate: date,
                            method: matchingMethod
                        });
                        matchColorIndex++;
                    }
                    
                    // Calculate capital gain/loss
                    const saleProceeds = settlValue;
                    const capitalGain = saleProceeds - totalCostBase;
                    
                    // Calculate holding period and CGT discount
                    let discountApplied = false;
                    let cgtDiscount = 0;
                    let holdingPeriod = 0;
                    
                    if (oldestBuyDate) {
                        holdingPeriod = calculateHoldingPeriod(oldestBuyDate, date);
                        if (holdingPeriod >= 365 && capitalGain > 0) { // 12+ months and gain
                            discountApplied = true;
                            const discountRate = parseFloat(document.getElementById('cgtDiscount').value) / 100;
                            cgtDiscount = capitalGain * discountRate;
                        }
                    }
                    
                    processedTx.capitalGain = capitalGain;
                    processedTx.cgtDiscount = cgtDiscount;
                    processedTx.holdingPeriod = holdingPeriod;
                    processedTx.discountApplied = discountApplied;
                }
                
                processed.push(processedTx);
            }

            return { transactions: processed, matches: matches, method: matchingMethod };
        }

        function displayResults(processedData) {
            // Update global variable for PDF generation
            processedTransactions = processedData.transactions;
            const matches = processedData.matches;
            const method = processedData.method;

            const resultsSection = document.getElementById('resultsSection');
            const autoPrefilledSection = document.getElementById('autoPrefilledSection');
            const assetDetailsSection = document.getElementById('assetDetailsSection');
            const atoSection = document.getElementById('atoSection');
            resultsSection.classList.remove('hidden');
            autoPrefilledSection.classList.remove('hidden');
            assetDetailsSection.classList.remove('hidden');
            atoSection.classList.remove('hidden');

            // Calculate totals
            let totalGains = 0;
            let totalLosses = 0;
            let netCapitalGain = 0;
            let totalCgtDiscount = 0;

            processedTransactions.forEach(tx => {
                if (tx.action === 'sell') {
                    totalCgtDiscount += tx.cgtDiscount;
                    const adjustedGain = tx.capitalGain - tx.cgtDiscount;
                    if (adjustedGain > 0) {
                        totalGains += adjustedGain;
                    } else {
                        totalLosses += Math.abs(adjustedGain);
                    }
                }
            });
            
            netCapitalGain = totalGains - totalLosses;
            
            // Calculate tax liability
            const salary = parseFloat(document.getElementById('annualSalary').value) || 0;
            const taxableIncome = salary + Math.max(0, netCapitalGain);
            const taxWithoutCGT = calculateTax(salary);
            const taxWithCGT = calculateTax(taxableIncome);
            const additionalTax = Math.max(0, taxWithCGT - taxWithoutCGT);
            
            // Update summary cards with color coding
            document.getElementById('totalGains').textContent = `$${totalGains.toFixed(2)}`;
            document.getElementById('totalLosses').textContent = `$${totalLosses.toFixed(2)}`;
            document.getElementById('netGain').textContent = `$${netCapitalGain.toFixed(2)}`;
            document.getElementById('taxLiability').textContent = `$${additionalTax.toFixed(2)}`;

            // Color code the summary cards
            const gainsCard = document.getElementById('totalGains').closest('.summary-card');
            const lossesCard = document.getElementById('totalLosses').closest('.summary-card');
            const netGainCard = document.getElementById('netGain').closest('.summary-card');
            const taxCard = document.getElementById('taxLiability').closest('.summary-card');

            // Reset classes
            [gainsCard, lossesCard, netGainCard, taxCard].forEach(card => {
                card.classList.remove('profit-card', 'loss-card', 'neutral-card');
            });

            // Apply appropriate colors
            if (totalGains > 0) gainsCard.classList.add('profit-card');
            else gainsCard.classList.add('neutral-card');

            if (totalLosses > 0) lossesCard.classList.add('loss-card');
            else lossesCard.classList.add('neutral-card');

            if (netCapitalGain > 0) netGainCard.classList.add('profit-card');
            else if (netCapitalGain < 0) netGainCard.classList.add('loss-card');
            else netGainCard.classList.add('neutral-card');

            if (additionalTax > 0) taxCard.classList.add('loss-card'); // Tax is a cost
            else taxCard.classList.add('neutral-card');

            // Update ATO summary
            updateAtoSummary(totalGains, totalLosses, netCapitalGain, totalCgtDiscount);

            // Update auto-prefilled questions
            updateAutoPrefilledQuestions();

            // Update individual asset details
            updateAssetDetails();
            
            // Display tax bracket information
            const bracket = getTaxBracket(taxableIncome);
            const bracketInfo = document.getElementById('taxBracketDetails');
            const netGainColor = netCapitalGain >= 0 ? '#4CAF50' : '#f44336';
            const netGainIcon = netCapitalGain >= 0 ? '📈' : '📉';
            const taxColor = additionalTax > 0 ? '#f44336' : '#4CAF50';

            bracketInfo.innerHTML = `
                <p><strong>Salary:</strong> $${salary.toLocaleString()}</p>
                <p><strong>Net Capital Gain:</strong>
                    <span style="color: ${netGainColor}; font-weight: bold;">
                        ${netGainIcon} ${netCapitalGain >= 0 ? '+' : ''}$${netCapitalGain.toFixed(2)}
                    </span>
                </p>
                <p><strong>Total Taxable Income:</strong> $${taxableIncome.toLocaleString()}</p>
                <p><strong>Tax Rate:</strong> ${(bracket.rate * 100).toFixed(1)}% + 2% Medicare Levy</p>
                <p><strong>Additional Tax from CGT:</strong>
                    <span style="color: ${taxColor}; font-weight: bold;">
                        $${additionalTax.toFixed(2)}
                    </span>
                </p>
            `;

            // Update method title and description
            updateMatchingMethodDisplay(method);

            // Display matching legend
            displayMatchingLegend(matches);

            // Populate transactions table
            const tbody = document.getElementById('transactionsBody');
            tbody.innerHTML = '';
            
            processedTransactions.forEach((tx, index) => {
                const row = tbody.insertRow();
                let rowClass = tx.action === 'buy' ? 'buy-row' : 'sell-row';
                if (tx.fifoMatchId) {
                    rowClass += ` fifo-${tx.fifoMatchId}`;
                }
                if (tx.sold) {
                    rowClass += ' sold-transaction';
                }
                row.className = rowClass;

                // Format remaining quantity display
                let remainingDisplay = '-';
                if (tx.action === 'buy') {
                    if (tx.remainingQuantity === tx.quantity) {
                        remainingDisplay = `${tx.remainingQuantity.toLocaleString()} (100%)`;
                    } else if (tx.remainingQuantity > 0) {
                        const percentRemaining = ((tx.remainingQuantity / tx.quantity) * 100).toFixed(1);
                        remainingDisplay = `${tx.remainingQuantity.toLocaleString()} (${percentRemaining}%)`;
                    } else {
                        remainingDisplay = `0 (0%)`;
                    }
                }

                // Format sold checkbox - only for buy transactions
                let soldCheckboxDisplay = '-';
                if (tx.action === 'buy') {
                    soldCheckboxDisplay = `<input type="checkbox" class="sold-checkbox" ${tx.sold ? 'checked' : ''}
                        onchange="toggleSoldStatus(${index})" title="Mark as already sold">`;
                }

                // Format matching details - only for buy transactions
                let matchingDetailsDisplay = '-';
                if (tx.action === 'buy' && tx.fifoMatchId) {
                    const usedQuantity = tx.quantity - tx.remainingQuantity;
                    if (usedQuantity > 0) {
                        const percentUsed = ((usedQuantity / tx.quantity) * 100).toFixed(1);
                        matchingDetailsDisplay = `<div style="font-size: 0.9em; color: #FFC107;">Used: ${usedQuantity.toLocaleString()} (${percentUsed}%)</div>`;
                    }
                }

                row.innerHTML = `
                    <td>${tx.date.toLocaleDateString('en-AU')}</td>
                    <td>${tx.code}</td>
                    <td>${tx.action.toUpperCase()}</td>
                    <td>${tx.quantity.toLocaleString()}</td>
                    <td>${remainingDisplay}</td>
                    <td>${soldCheckboxDisplay}</td>
                    <td>$${tx.price.toFixed(3)}</td>
                    <td>$${tx.fees.toFixed(2)}</td>
                    <td>$${tx.settlValue.toFixed(2)}</td>
                    <td>${matchingDetailsDisplay}</td>
                    <td class="${tx.capitalGain >= 0 ? 'gain' : 'loss'}">
                        ${tx.action === 'sell' ?
                            `${tx.capitalGain >= 0 ? '📈 +' : '📉 '}$${Math.abs(tx.capitalGain).toFixed(2)}` :
                            '-'}
                    </td>
                    <td>${tx.action === 'sell' && tx.discountApplied ? `$${tx.cgtDiscount.toFixed(2)}` : '-'}</td>
                    <td>${tx.action === 'sell' ? `${tx.holdingPeriod} days` : '-'}</td>
                `;
            });
        }

        function updateMatchingMethodDisplay(method) {
            const methodTitles = {
                'fifo': 'FIFO',
                'hifo': 'HIFO',
                'lofo': 'LOFO',
                'lifo': 'LIFO'
            };

            const methodDescriptions = {
                'fifo': 'Each colored section shows how sales are matched to purchases using First-In-First-Out (FIFO) method - oldest shares sold first.',
                'hifo': 'Each colored section shows how sales are matched to purchases using Highest-In-First-Out (HIFO) method - highest cost shares sold first to minimize capital gains.',
                'lofo': 'Each colored section shows how sales are matched to purchases using Lowest-In-First-Out (LOFO) method - lowest cost shares sold first to maximize capital gains.',
                'lifo': 'Each colored section shows how sales are matched to purchases using Last-In-First-Out (LIFO) method - newest shares sold first to preserve long-term holdings.'
            };

            document.getElementById('matchingMethodTitle').textContent = methodTitles[method] || 'FIFO';
            document.getElementById('matchingMethodDescription').textContent = methodDescriptions[method] || methodDescriptions['fifo'];
        }

        function displayMatchingLegend(matches) {
            const legendContainer = document.getElementById('fifoLegendItems');
            legendContainer.innerHTML = '';

            matches.forEach((match, index) => {
                const colorClass = `fifo-match-${(index % 8) + 1}`;
                const legendItem = document.createElement('div');
                legendItem.className = `legend-item ${colorClass}`;

                // Create detailed match information with method-specific sorting info
                const buyDetails = match.buyTransactions.map(buy => {
                    const percentageUsed = ((buy.quantity / buy.totalQuantity) * 100).toFixed(1);
                    const remaining = buy.remainingAfter;
                    const remainingText = remaining > 0 ? ` (${remaining.toLocaleString()} remaining)` : ' (fully used)';
                    return `• ${buy.quantity.toLocaleString()} from ${buy.buyDate.toLocaleDateString('en-AU')} @ $${buy.buyPrice.toFixed(3)} - ${percentageUsed}% used${remainingText}`;
                }).join('<br>');

                const methodText = match.method === 'hifo' ? ' (Highest Cost First)' :
                                 match.method === 'lofo' ? ' (Lowest Cost First)' :
                                 ' (Chronological Order)';

                legendItem.innerHTML = `
                    <div style="font-weight: bold; margin-bottom: 5px;">
                        ${match.code} Sale #${index + 1} - ${match.sellQuantity.toLocaleString()} units on ${match.sellDate.toLocaleDateString('en-AU')}${methodText}
                    </div>
                    <div style="font-size: 0.9em; line-height: 1.4; margin-left: 10px;">
                        ${buyDetails}
                    </div>
                `;
                legendContainer.appendChild(legendItem);
            });
        }

        function updateAtoSummary(totalGains, totalLosses, netCapitalGain, totalCgtDiscount) {
            // Calculate total current year capital gains (before any reductions) - Label H
            let totalCurrentYearGains = 0;
            processedTransactions.forEach(tx => {
                if (tx.action === 'sell' && tx.capitalGain > 0) {
                    totalCurrentYearGains += tx.capitalGain; // Before CGT discount
                }
            });

            // Label G: Did you have a CGT event? (Always YES if we have transactions)
            document.getElementById('atoLabelG').textContent = 'YES';

            // Label H: Total current year capital gains (before applying losses and discounts)
            document.getElementById('atoLabelH').textContent = `$${totalCurrentYearGains.toFixed(2)}`;

            // Label A: Net capital gain (after applying losses and CGT discount)
            if (netCapitalGain > 0) {
                document.getElementById('atoLabelA').textContent = `$${netCapitalGain.toFixed(2)}`;
                document.getElementById('atoLabelVSection').style.display = 'none';
            } else {
                document.getElementById('atoLabelA').textContent = '$0.00';
            }

            // Label V: Net capital losses carried forward (only show if there are net losses)
            if (netCapitalGain < 0) {
                document.getElementById('atoLabelV').textContent = `$${Math.abs(netCapitalGain).toFixed(2)}`;
                document.getElementById('atoLabelVSection').style.display = 'block';
            } else {
                document.getElementById('atoLabelVSection').style.display = 'none';
            }

            // Label M: Exemptions/rollovers (currently set to NO - could be enhanced later)
            document.getElementById('atoLabelM').textContent = 'NO';
            document.getElementById('atoLabelMCode').style.display = 'none';

            // Additional information: CGT discount applied
            document.getElementById('atoCgtDiscount').textContent = `$${totalCgtDiscount.toFixed(2)}`;
        }

        function getIncomeYearValidation(date) {
            // Format the date for display
            const formattedDate = date.toLocaleDateString('en-AU');

            // Determine which Australian financial year this date falls into
            // Australian financial year runs July 1 to June 30
            const year = date.getFullYear();
            const month = date.getMonth(); // 0-11

            let financialYear;
            if (month >= 6) { // July onwards (month 6+)
                financialYear = `${year}-${(year + 1).toString().slice(-2)}`;
            } else { // January to June
                financialYear = `${year - 1}-${year.toString().slice(-2)}`;
            }

            return `${formattedDate} (FY ${financialYear})`;
        }

        function updateAutoPrefilledQuestions() {
            // Determine income year based on transaction dates
            let latestYear = new Date().getFullYear();
            let earliestYear = latestYear;

            processedTransactions.forEach(tx => {
                const year = tx.date.getFullYear();
                if (year > latestYear) latestYear = year;
                if (year < earliestYear) earliestYear = year;
            });

            // Australian financial year runs July 1 to June 30
            // If transactions are in the current calendar year, determine if it's current or next FY
            const now = new Date();
            const currentMonth = now.getMonth(); // 0-11
            let financialYear;

            if (currentMonth >= 6) { // July onwards (month 6+)
                financialYear = `${latestYear}-${(latestYear + 1).toString().slice(-2)}`;
            } else { // January to June
                financialYear = `${latestYear - 1}-${latestYear.toString().slice(-2)}`;
            }

            document.getElementById('autoIncomeYear').textContent = financialYear;
        }

        function updateAssetDetails() {
            const assetDetailsList = document.getElementById('assetDetailsList');
            assetDetailsList.innerHTML = '';

            // Get all sell transactions
            const sellTransactions = processedTransactions.filter(tx => tx.action === 'sell');

            let assetDetailIndex = 1;

            sellTransactions.forEach((sellTx, sellIndex) => {
                // Find the matched buy transactions for this sale
                const matchedBuys = sellTx.matchedTransactions || [];

                // Create individual asset detail card for each matched buy transaction
                matchedBuys.forEach((matchedBuy, buyIndex) => {
                    const assetCard = document.createElement('div');
                    assetCard.className = 'ato-item';
                    assetCard.style.borderLeft = '4px solid #2196F3';
                    assetCard.style.background = 'rgba(33, 150, 243, 0.1)';

                    // Calculate the portion of the sale that corresponds to this buy
                    const quantityUsed = matchedBuy.quantity;
                    const proportionOfSale = quantityUsed / sellTx.quantity;
                    const saleProceeds = (sellTx.settlValue * proportionOfSale);

                    // Calculate costs separately for clarity
                    const purchaseCost = matchedBuy.quantity * matchedBuy.buyPrice;
                    const allocatedFees = matchedBuy.fees || 0;
                    const costWithoutBrokerage = purchaseCost;
                    const costWithBrokerage = purchaseCost + allocatedFees;
                    const costBase = costWithBrokerage; // Cost base includes all fees
                    const capitalGain = saleProceeds - costBase;

                    // Calculate holding period for this specific buy-sell pair
                    const holdingPeriod = Math.ceil((sellTx.date - matchedBuy.buyDate) / (1000 * 60 * 60 * 24));
                    const discountApplied = holdingPeriod > 365 && capitalGain > 0;
                    const cgtDiscount = discountApplied ? capitalGain * 0.5 : 0;

                    // Extract just the stock code without .ASX suffix
                    const stockCode = sellTx.code.replace('.ASX', '');

                    assetCard.innerHTML = `
                        <h4 style="color: #2196F3;">Asset #${assetDetailIndex}: ${stockCode} - ${quantityUsed.toLocaleString()} shares</h4>
                        <p style="color: #888; margin: 5px 0; font-size: 0.9em;">From purchase on ${matchedBuy.buyDate.toLocaleDateString('en-AU')} → Sold on ${sellTx.date.toLocaleDateString('en-AU')}</p>

                        <div style="margin: 15px 0;">
                            <!-- Asset Type -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Asset type:</label>
                                <input type="text" value="Shares in companies listed on an Australian stock exchange"
                                       style="width: 100%; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;" readonly>
                            </div>

                            <!-- ASX / Investment Code -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">ASX / Investment code:</label>
                                <div style="display: flex; gap: 10px; align-items: center;">
                                    <input type="text" value="${stockCode}"
                                           style="flex: 1; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;" readonly>
                                    <button class="copy-button" onclick="copyText('${stockCode}')">📋</button>
                                </div>
                            </div>

                            <!-- Description -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Description:</label>
                                <input type="text" value="${stockCode} ordinary shares"
                                       style="width: 100%; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;" readonly>
                            </div>

                            <!-- Date Acquired -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Date acquired:</label>
                                <div style="display: flex; gap: 10px; align-items: center;">
                                    <input type="text" value="${matchedBuy.buyDate.toLocaleDateString('en-AU')}"
                                           style="flex: 1; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;" readonly>
                                    <button class="copy-button" onclick="copyText('${matchedBuy.buyDate.toLocaleDateString('en-AU')}')">📋</button>
                                </div>
                            </div>

                            <!-- Date Disposed -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Date disposed:</label>
                                <div style="display: flex; gap: 10px; align-items: center;">
                                    <input type="text" value="${sellTx.date.toLocaleDateString('en-AU')}"
                                           style="flex: 1; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;" readonly>
                                    <button class="copy-button" onclick="copyText('${sellTx.date.toLocaleDateString('en-AU')}')">📋</button>
                                </div>
                            </div>
                        </div>

                        <!-- Income Year Validation Warning -->
                        <div style="margin: 15px 0; padding: 10px; background: rgba(255, 152, 0, 0.1); border-left: 4px solid #FF9800; border-radius: 5px;">
                            <div style="display: flex; align-items: center; color: #FF9800;">
                                <span style="margin-right: 8px;">⚠️</span>
                                <span style="font-size: 0.9em;">
                                    The disposal date of this asset is ${getIncomeYearValidation(sellTx.date)}. Only assets with a disposal date within the selected income year are included in the calculation.
                                </span>
                            </div>
                        </div>

                        <!-- Additional Questions -->
                        <div style="margin: 15px 0;">
                            <h5 style="color: #2196F3; margin-bottom: 15px;">Additional Questions for this Asset:</h5>

                            <!-- Full Exemption or Rollover -->
                            <div style="margin-bottom: 20px; padding: 15px; background: rgba(50, 50, 70, 0.8); border-radius: 8px;">
                                <p style="margin-bottom: 10px; font-weight: bold;">Have you applied any full exemption or rollover? *</p>
                                <div style="margin: 10px 0;">
                                    <label style="display: flex; align-items: center; margin: 8px 0; cursor: pointer;">
                                        <input type="radio" name="exemption_${assetDetailIndex}" value="yes" style="margin-right: 10px;">
                                        <span>Yes</span>
                                    </label>
                                    <label style="display: flex; align-items: center; margin: 8px 0; cursor: pointer;">
                                        <input type="radio" name="exemption_${assetDetailIndex}" value="no" checked style="margin-right: 10px;">
                                        <span style="color: #4CAF50; font-weight: bold;">No</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Discount/Gain/Loss Calculation -->
                            <div style="margin-bottom: 20px; padding: 15px; background: rgba(50, 50, 70, 0.8); border-radius: 8px;">
                                <p style="margin-bottom: 10px; font-weight: bold;">Have you calculated a discount, an indexed or other gain or a loss? *</p>
                                <div style="margin: 10px 0;">
                                    <label style="display: flex; align-items: center; margin: 8px 0; cursor: pointer;">
                                        <input type="radio" name="calculation_${assetDetailIndex}" value="discount" ${discountApplied ? 'checked' : ''} style="margin-right: 10px;">
                                        <span ${discountApplied ? 'style="color: #4CAF50; font-weight: bold;"' : ''}>Discount gain</span>
                                    </label>
                                    <label style="display: flex; align-items: center; margin: 8px 0; cursor: pointer;">
                                        <input type="radio" name="calculation_${assetDetailIndex}" value="indexed" style="margin-right: 10px;">
                                        <span>Indexed or other gain</span>
                                    </label>
                                    <label style="display: flex; align-items: center; margin: 8px 0; cursor: pointer;">
                                        <input type="radio" name="calculation_${assetDetailIndex}" value="loss" ${capitalGain < 0 ? 'checked' : ''} style="margin-right: 10px;">
                                        <span ${capitalGain < 0 ? 'style="color: #4CAF50; font-weight: bold;"' : ''}>Loss</span>
                                    </label>
                                    <label style="display: flex; align-items: center; margin: 8px 0; cursor: pointer;">
                                        <input type="radio" name="calculation_${assetDetailIndex}" value="calculate" ${!discountApplied && capitalGain >= 0 ? 'checked' : ''} style="margin-right: 10px;">
                                        <span ${!discountApplied && capitalGain >= 0 ? 'style="color: #4CAF50; font-weight: bold;"' : ''}>No, I need to calculate</span>
                                    </label>
                                </div>
                            </div>



                        <!-- Cost Details Section -->
                        <div style="margin: 15px 0; padding: 20px; background: rgba(50, 50, 70, 0.8); border-radius: 8px;">
                            <!-- Header -->
                            <div style="margin-bottom: 20px;">
                                <h5 style="color: #4CAF50; margin: 0;">Add costs incurred</h5>
                            </div>

                            <!-- Table Header -->
                            <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; gap: 15px; padding: 12px 0; border-bottom: 1px solid #555; font-weight: bold; color: #b0b0b0; margin-bottom: 15px;">
                                <div>Description</div>
                                <div>Cost</div>
                                <div>Cost base</div>
                                <div>Status</div>
                            </div>

                            <!-- Cost Entry Row -->
                            <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; gap: 15px; align-items: center; padding: 15px 0; border-bottom: 1px solid #444;">
                                <div>
                                    <div style="color: #4CAF50; font-weight: bold; margin-bottom: 4px;">${stockCode}</div>
                                    <div style="font-size: 0.85em; color: #888;">
                                        ${quantityUsed.toLocaleString()} shares @ $${matchedBuy.buyPrice.toFixed(3)} on ${matchedBuy.buyDate.toLocaleDateString('en-AU')}
                                    </div>
                                </div>
                                <div style="color: #4CAF50; font-weight: bold;">$${costBase.toFixed(2)}</div>
                                <div style="color: #4CAF50; font-weight: bold;">$${costBase.toFixed(2)}</div>
                                <div style="text-align: center;">
                                    <span style="color: #4CAF50; font-size: 18px;">✓</span>
                                </div>
                            </div>

                            <!-- Info Message -->
                            <div style="margin-top: 20px; padding: 15px; background: rgba(33, 150, 243, 0.1); border-left: 4px solid #2196F3; border-radius: 4px;">
                                <div style="display: flex; align-items: flex-start; color: #2196F3; font-size: 0.9em;">
                                    <span style="margin-right: 12px; margin-top: 2px;">ℹ️</span>
                                    <span>You can add multiple costs for each event e.g. Purchase price and brokerage or stamp duty.</span>
                                </div>
                            </div>

                            <!-- Cost Input Form -->
                            <div style="margin-top: 25px; padding: 20px; background: rgba(40, 40, 60, 0.8); border-radius: 8px;">
                                <!-- Cost Type -->
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Cost type <span style="color: #f44336;">*</span></label>
                                    <select style="width: 100%; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;">
                                        <option selected>Acquisition or purchase cost of the CGT asset</option>
                                        <option>Brokerage fees</option>
                                        <option>Stamp duty</option>
                                        <option>Other costs</option>
                                    </select>
                                </div>

                                <!-- Description -->
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Description <span style="color: #f44336;">*</span></label>
                                    <input type="text" value="${stockCode}"
                                           style="width: 100%; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;" readonly>
                                </div>

                                <!-- Date Cost Incurred -->
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Date cost incurred <span style="color: #f44336;">*</span></label>
                                    <input type="text" value="${matchedBuy.buyDate.toLocaleDateString('en-AU')}"
                                           style="width: 100%; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;" readonly>
                                </div>

                                <!-- Cost -->
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Cost <span style="color: #f44336;">*</span></label>
                                    <div style="display: flex; gap: 0; align-items: center;">
                                        <span style="background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-right: none; border-radius: 8px 0 0 8px; padding: 12px; color: #4CAF50; font-weight: bold;">$</span>
                                        <input type="text" value="${costBase.toFixed(0)}"
                                               style="flex: 1; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 0; color: #4CAF50; font-weight: bold;" readonly>
                                        <span style="background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-left: none; border-radius: 0 8px 8px 0; padding: 12px; color: #4CAF50; font-weight: bold;">.00</span>
                                    </div>
                                </div>

                                <!-- Amount Excluded from Cost -->
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Amount excluded from cost</label>
                                    <div style="display: flex; gap: 0; align-items: center;">
                                        <span style="background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-right: none; border-radius: 8px 0 0 8px; padding: 12px; color: #4CAF50; font-weight: bold;">$</span>
                                        <input type="text" value="" placeholder="0"
                                               style="flex: 1; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 0; color: #4CAF50; font-weight: bold;">
                                        <span style="background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-left: none; border-radius: 0 8px 8px 0; padding: 12px; color: #4CAF50; font-weight: bold;">.00</span>
                                    </div>
                                </div>

                                <!-- Cost Base -->
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Cost base</label>
                                    <div style="color: #4CAF50; font-weight: bold; font-size: 18px;">$${costBase.toFixed(2)}</div>
                                </div>

                                <!-- Capital Proceeds -->
                                <div style="margin-bottom: 20px;">
                                    <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Capital Proceeds</label>
                                    <div style="display: flex; gap: 10px; align-items: center;">
                                        <input type="text" value="$${saleProceeds.toFixed(2)}"
                                               style="flex: 1; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;" readonly>
                                        <button class="copy-button" onclick="copyText('${saleProceeds.toFixed(2)}')">📋</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Summary Information -->
                        <div style="margin: 15px 0;">
                            <h5 style="color: #2196F3; margin-bottom: 15px;">Summary Information:</h5>

                            <!-- Quantity Sold -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Quantity sold:</label>
                                <div style="display: flex; gap: 10px; align-items: center;">
                                    <input type="text" value="${quantityUsed.toLocaleString()}"
                                           style="flex: 1; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;" readonly>
                                    <button class="copy-button" onclick="copyText('${quantityUsed}')">📋</button>
                                </div>
                            </div>



                            <!-- Cost (without brokerage) -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Cost (without brokerage):</label>
                                <div style="display: flex; gap: 10px; align-items: center;">
                                    <input type="text" value="$${costWithoutBrokerage.toFixed(2)}"
                                           style="flex: 1; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;" readonly>
                                    <button class="copy-button" onclick="copyText('${costWithoutBrokerage.toFixed(2)}')">📋</button>
                                </div>
                            </div>

                            <!-- Cost (with brokerage) -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Cost (with brokerage):</label>
                                <div style="display: flex; gap: 10px; align-items: center;">
                                    <input type="text" value="$${costWithBrokerage.toFixed(2)}"
                                           style="flex: 1; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;" readonly>
                                    <button class="copy-button" onclick="copyText('${costWithBrokerage.toFixed(2)}')">📋</button>
                                </div>
                            </div>

                            <!-- Capital Gain/Loss -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Capital gain/loss:</label>
                                <div style="display: flex; gap: 10px; align-items: center;">
                                    <input type="text" value="${capitalGain >= 0 ? '+' : ''}$${capitalGain.toFixed(2)}"
                                           style="flex: 1; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: ${capitalGain >= 0 ? '#4CAF50' : '#f44336'}; font-weight: bold;" readonly>
                                    <button class="copy-button" onclick="copyText('${capitalGain.toFixed(2)}')">📋</button>
                                </div>
                            </div>

                            <!-- CGT Discount Applied -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">CGT discount applied:</label>
                                <input type="text" value="$${cgtDiscount.toFixed(2)}"
                                       style="width: 100%; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;" readonly>
                            </div>

                            <!-- Holding Period -->
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; color: #b0b0b0; margin-bottom: 8px; font-weight: bold;">Holding period:</label>
                                <input type="text" value="${holdingPeriod} days"
                                       style="width: 100%; padding: 12px; background: rgba(50, 50, 70, 0.8); border: 1px solid #555; border-radius: 8px; color: #4CAF50; font-weight: bold;" readonly>
                            </div>
                        </div>

                        <div style="margin-top: 15px; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 5px;">
                            <h5 style="color: #4CAF50; margin-bottom: 10px;">📋 Paper Matching Details (${document.getElementById('matchingMethod').options[document.getElementById('matchingMethod').selectedIndex].text}):</h5>
                            <div style="font-size: 0.9em; color: #b0b0b0;">
                                • ${quantityUsed.toLocaleString()} shares from purchase on ${matchedBuy.buyDate.toLocaleDateString('en-AU')} @ $${matchedBuy.buyPrice.toFixed(3)} (Cost: $${costBase.toFixed(2)})
                            </div>
                        </div>
                    `;

                    assetDetailsList.appendChild(assetCard);
                    assetDetailIndex++;
                });
            });

            if (sellTransactions.length === 0) {
                assetDetailsList.innerHTML = '<div class="ato-item"><p style="color: #888;">No share sales found in your transactions.</p></div>';
            }
        }

        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent.replace('$', '').replace(',', '');

            navigator.clipboard.writeText(text).then(() => {
                // Show success feedback
                const button = element.nextElementSibling;
                const originalText = button.textContent;
                button.textContent = '✅ Copied!';
                button.style.background = 'linear-gradient(45deg, #4CAF50, #45a049)';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'linear-gradient(45deg, #FF9800, #F57C00)';
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
                alert('Failed to copy to clipboard. Please copy manually: ' + text);
            });
        }

        function copyText(text) {
            // Clean the text for copying (remove $ and commas for numbers)
            const cleanText = text.toString().replace(/[$,]/g, '');

            navigator.clipboard.writeText(cleanText).then(() => {
                // Find the button that was clicked and show success feedback
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '✅ Copied!';
                button.style.background = 'linear-gradient(45deg, #4CAF50, #45a049)';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'linear-gradient(45deg, #FF9800, #F57C00)';
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
                alert('Failed to copy to clipboard. Please copy manually: ' + cleanText);
            });
        }

        function processData() {
            const fileInput = document.getElementById('csvFile');
            const salaryInput = document.getElementById('annualSalary');
            
            if (!fileInput.files[0]) {
                alert('Please select a CSV file');
                return;
            }
            
            if (!salaryInput.value) {
                alert('Please enter your annual salary');
                return;
            }
            
            const file = fileInput.files[0];
            const reader = new FileReader();
            
            reader.onload = function(e) {
                try {
                    const csvData = parseCSV(e.target.result);
                    const processedData = processTransactions(csvData);
                    displayResults(processedData);
                    
                    // Show success message
                    const successDiv = document.createElement('div');
                    successDiv.className = 'success';
                    successDiv.textContent = `Successfully processed ${csvData.length} transactions`;
                    document.querySelector('.container').insertBefore(successDiv, document.getElementById('resultsSection'));
                    
                    setTimeout(() => successDiv.remove(), 5000);
                    
                } catch (error) {
                    console.error('Error processing CSV:', error);
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'error';
                    errorDiv.textContent = `Error processing CSV: ${error.message}`;
                    document.querySelector('.container').appendChild(errorDiv);
                }
            };
            
            reader.readAsText(file);
        }

        // ATO PDF Report Generation
        function generateATOReport(summary) {
            if (processedTransactions.length === 0) {
                alert('No transaction data available. Please process your transactions first.');
                return;
            }

            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // Set up document properties
            doc.setProperties({
                title: 'ATO Capital Gains Tax Report',
                subject: 'Australian Tax Office CGT Compliance Report',
                author: 'Bradley\'s CGT Calculator',
                creator: 'CGT Calculator & Portfolio Tracker'
            });

            let yPosition = 20;
            const pageHeight = doc.internal.pageSize.height;
            const margin = 20;
            const lineHeight = 7;

            // Helper function to check if we need a new page
            function checkNewPage(requiredSpace = 20) {
                if (yPosition + requiredSpace > pageHeight - margin) {
                    doc.addPage();
                    yPosition = 20;
                    return true;
                }
                return false;
            }

            // Helper function to add section header
            function addSectionHeader(title, color = [33, 150, 243]) {
                checkNewPage(30);
                doc.setFillColor(color[0], color[1], color[2]);
                doc.rect(margin, yPosition, 170, 10, 'F');
                doc.setTextColor(255, 255, 255);
                doc.setFontSize(12);
                doc.setFont('helvetica', 'bold');
                doc.text(title, margin + 5, yPosition + 7);
                doc.setTextColor(0, 0, 0);
                yPosition += 15;
            }

            // Title and Header
            doc.setFontSize(20);
            doc.setFont('helvetica', 'bold');
            doc.setTextColor(76, 175, 80);
            doc.text('AUSTRALIAN TAX OFFICE', 105, yPosition, { align: 'center' });
            yPosition += 10;

            doc.setFontSize(16);
            doc.setTextColor(0, 0, 0);
            doc.text('Capital Gains Tax Report', 105, yPosition, { align: 'center' });
            yPosition += 15;

            // Report metadata
            doc.setFontSize(10);
            doc.setFont('helvetica', 'normal');
            const currentDate = new Date().toLocaleDateString('en-AU');
            const taxYear = new Date().getFullYear();

            doc.text(`Report Generated: ${currentDate}`, margin, yPosition);
            doc.text(`Tax Year: ${taxYear - 1}-${taxYear}`, 150, yPosition);
            yPosition += 10;

            doc.text(`Total Transactions Processed: ${processedTransactions.length}`, margin, yPosition);
            doc.text(`Matching Method: ${document.getElementById('matchingMethod').value.toUpperCase()}`, 150, yPosition);
            yPosition += 15;

            // ATO Compliance Statement
            addSectionHeader('ATO COMPLIANCE STATEMENT', [255, 152, 0]);
            doc.setFontSize(9);
            doc.setFont('helvetica', 'normal');
            const complianceText = [
                'This report has been generated in accordance with Australian Taxation Office (ATO) record-keeping requirements',
                'for Capital Gains Tax (CGT) as outlined in the Income Tax Assessment Act 1997.',
                '',
                'ATO Item 18 Capital Gains Compliance:',
                '• Label G: CGT event occurred during the year (YES)',
                '• Label H: Total current year capital gains (before applying losses and discounts)',
                '• Label A: Net capital gain (after applying losses and CGT discount)',
                '• Label V: Net capital losses carried forward to later income years (if applicable)',
                '• Label M: Exemptions or rollovers applied (if applicable)',
                '',
                'Records include:',
                '• Date of acquisition and disposal for each CGT asset',
                '• Cost base and reduced cost base calculations',
                '• Details of all relevant CGT events',
                '• Brokerage fees, legal costs, and other incidental costs',
                '• CGT discount applications for assets held longer than 12 months',
                '',
                'This report should be retained for at least 5 years after disposal of each asset as required by ATO guidelines.',
                'Refer to ATO Guide to capital gains tax 2024 for complete instructions.'
            ];

            complianceText.forEach(line => {
                checkNewPage();
                doc.text(line, margin, yPosition);
                yPosition += lineHeight;
            });
            yPosition += 10;

            const netCapitalGain = summary.netCapitalGain;
            const salary = parseFloat(document.getElementById('annualSalary').value) || 0;

            // Tax Return Summary
            addSectionHeader('TAX RETURN SUMMARY', [255, 152, 0]);
            doc.setFontSize(11);
            doc.setFont('helvetica', 'bold');

            const totalCurrentYearGains = summary.totalGains + summary.totalLosses;

            const summaryData = [
                ['Item 18 - Label G (CGT Event):', 'YES'],
                ['Item 18 - Label H (Total Current Year Capital Gains):', `$${totalCurrentYearGains.toFixed(2)}`],
                ['Item 18 - Label A (Net Capital Gain):', `$${Math.max(0, netCapitalGain).toFixed(2)}`],
                ['Item 18 - Label V (Net Capital Losses Carried Forward):', netCapitalGain < 0 ? `$${Math.abs(netCapitalGain).toFixed(2)}` : '$0.00'],
                ['Item 18 - Label M (Exemptions/Rollovers):', 'NO'],
                ['Total CGT Discount Applied:', `$${totalCgtDiscount.toFixed(2)}`],
                ['Annual Salary:', `$${salary.toLocaleString()}`],
                ['Total Taxable Income:', `$${(salary + Math.max(0, netCapitalGain)).toLocaleString()}`]
            ];

            summaryData.forEach(([label, value]) => {
                checkNewPage();
                doc.setFont('helvetica', 'normal');
                doc.text(label, margin, yPosition);
                doc.setFont('helvetica', 'bold');
                doc.text(value, 120, yPosition);
                yPosition += lineHeight + 2;
            });
            yPosition += 10;

            // Detailed Transaction Records
            addSectionHeader('DETAILED TRANSACTION RECORDS', [76, 175, 80]);

            // Create transaction table
            const transactionTableData = [];
            transactionTableData.push([
                'Date', 'Code', 'Action', 'Quantity', 'Price', 'Fees', 'Total Value',
                'Capital Gain/Loss', 'CGT Discount', 'Holding Period'
            ]);

            processedTransactions.forEach(tx => {
                transactionTableData.push([
                    tx.date.toLocaleDateString('en-AU'),
                    tx.code,
                    tx.action.toUpperCase(),
                    tx.quantity.toLocaleString(),
                    `$${tx.price.toFixed(3)}`,
                    `$${tx.fees.toFixed(2)}`,
                    `$${tx.settlValue.toFixed(2)}`,
                    tx.action === 'sell' ?
                        `${tx.capitalGain >= 0 ? '+' : ''}$${tx.capitalGain.toFixed(2)}` : '-',
                    tx.action === 'sell' && tx.discountApplied ?
                        `$${tx.cgtDiscount.toFixed(2)}` : '-',
                    tx.action === 'sell' ? `${tx.holdingPeriod} days` : '-'
                ]);
            });

            // Add transaction table to PDF
            doc.autoTable({
                head: [transactionTableData[0]],
                body: transactionTableData.slice(1),
                startY: yPosition,
                styles: { fontSize: 8, cellPadding: 2 },
                headStyles: { fillColor: [76, 175, 80], textColor: 255 },
                alternateRowStyles: { fillColor: [245, 245, 245] },
                columnStyles: {
                    0: { cellWidth: 20 }, // Date
                    1: { cellWidth: 15 }, // Code
                    2: { cellWidth: 15 }, // Action
                    3: { cellWidth: 18 }, // Quantity
                    4: { cellWidth: 18 }, // Price
                    5: { cellWidth: 15 }, // Fees
                    6: { cellWidth: 20 }, // Total Value
                    7: { cellWidth: 25 }, // Capital Gain/Loss
                    8: { cellWidth: 18 }, // CGT Discount
                    9: { cellWidth: 20 }  // Holding Period
                },
                margin: { left: margin, right: margin }
            });

            yPosition = doc.lastAutoTable.finalY + 15;

            // Cost Base Analysis
            addSectionHeader('COST BASE ANALYSIS', [33, 150, 243]);

            // Group transactions by stock code for cost base analysis
            const stockAnalysis = new Map();
            processedTransactions.forEach(tx => {
                if (!stockAnalysis.has(tx.code)) {
                    stockAnalysis.set(tx.code, {
                        code: tx.code,
                        purchases: [],
                        sales: [],
                        totalPurchased: 0,
                        totalSold: 0,
                        totalCostBase: 0,
                        totalProceeds: 0,
                        netGainLoss: 0
                    });
                }

                const analysis = stockAnalysis.get(tx.code);
                if (tx.action === 'buy') {
                    analysis.purchases.push(tx);
                    analysis.totalPurchased += tx.quantity;
                    analysis.totalCostBase += tx.settlValue;
                } else if (tx.action === 'sell') {
                    analysis.sales.push(tx);
                    analysis.totalSold += tx.quantity;
                    analysis.totalProceeds += tx.settlValue;
                    analysis.netGainLoss += tx.capitalGain;
                }
            });

            // Create cost base table
            const costBaseTableData = [];
            costBaseTableData.push([
                'Stock Code', 'Total Purchased', 'Total Sold', 'Remaining',
                'Total Cost Base', 'Total Proceeds', 'Net Gain/Loss'
            ]);

            for (let [code, analysis] of stockAnalysis) {
                const remaining = analysis.totalPurchased - analysis.totalSold;
                costBaseTableData.push([
                    analysis.code,
                    analysis.totalPurchased.toLocaleString(),
                    analysis.totalSold.toLocaleString(),
                    remaining.toLocaleString(),
                    `$${analysis.totalCostBase.toFixed(2)}`,
                    `$${analysis.totalProceeds.toFixed(2)}`,
                    `${analysis.netGainLoss >= 0 ? '+' : ''}$${analysis.netGainLoss.toFixed(2)}`
                ]);
            }

            checkNewPage(50);
            doc.autoTable({
                head: [costBaseTableData[0]],
                body: costBaseTableData.slice(1),
                startY: yPosition,
                styles: { fontSize: 9, cellPadding: 3 },
                headStyles: { fillColor: [33, 150, 243], textColor: 255 },
                alternateRowStyles: { fillColor: [245, 245, 245] },
                margin: { left: margin, right: margin }
            });

            yPosition = doc.lastAutoTable.finalY + 15;

            // ATO Compliance Footer
            checkNewPage(40);
            addSectionHeader('RECORD RETENTION REQUIREMENTS', [244, 67, 54]);

            doc.setFontSize(9);
            doc.setFont('helvetica', 'normal');
            const footerText = [
                'IMPORTANT: This report must be retained for ATO compliance purposes.',
                '',
                'Retention Period: 5 years after disposal of each CGT asset',
                'Legal Basis: Income Tax Assessment Act 1997, Subdivision 121-A',
                '',
                'This report contains all information required by the ATO for CGT record-keeping including:',
                '• Acquisition and disposal dates for all CGT assets',
                '• Cost base calculations including incidental costs (brokerage, legal fees)',
                '• Capital gains and losses calculations',
                '• CGT discount applications where applicable',
                '• Matching method used for share disposals',
                '',
                'Generated by: Bradley\'s CGT Calculator & Portfolio Tracker',
                `Report Date: ${currentDate}`,
                'Version: Professional ATO Compliance Report v1.0'
            ];

            footerText.forEach(line => {
                checkNewPage();
                if (line === '') {
                    yPosition += lineHeight / 2;
                } else {
                    doc.text(line, margin, yPosition);
                    yPosition += lineHeight;
                }
            });

            // Save the PDF
            const fileName = `ATO_CGT_Report_${new Date().toISOString().split('T')[0]}.pdf`;
            doc.save(fileName);

            // Show success message
            alert(`ATO CGT Report generated successfully!\n\nFile: ${fileName}\n\nThis report contains all ATO-required information for CGT compliance and should be retained for 5 years after asset disposal.`);
        }

        // Initialize default values
        document.addEventListener('DOMContentLoaded', function() {
            // Set default CGT discount rate
            document.getElementById('cgtDiscount').value = 50;
            document.getElementById('medicareLevy').value = 2;
            // Set default matching method
            document.getElementById('matchingMethod').value = 'fifo';
        });
    </script>
</body>
</html>
