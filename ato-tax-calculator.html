<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>'s Tax Return Calculator</title>
    <!-- PDF Generation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #e0e0e0;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(30, 30, 50, 0.9);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        h1 {
            text-align: center;
            color: #4CAF50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .navigation {
            background: rgba(40, 40, 60, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 25px;
            border: 1px solid #2196F3;
            text-align: center;
        }

        .nav-link {
            display: inline-block;
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 10px;
            transition: all 0.3s;
            font-weight: 600;
        }

        .nav-link:hover {
            background: linear-gradient(45deg, #1976D2, #2196F3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
        }

        .progress-bar {
            background: rgba(40, 40, 60, 0.8);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            border: 1px solid #4CAF50;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .step {
            flex: 1;
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            margin: 0 5px;
            background: rgba(60, 60, 80, 0.5);
            border: 2px solid #555;
            transition: all 0.3s;
        }

        .step.active {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4CAF50;
            color: #4CAF50;
            font-weight: bold;
        }

        .step.completed {
            background: rgba(76, 175, 80, 0.2);
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .progress-line {
            height: 4px;
            background: #555;
            border-radius: 2px;
            position: relative;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            border-radius: 2px;
            transition: width 0.5s ease;
            width: 0%;
        }

        .wizard-section {
            background: rgba(40, 40, 60, 0.8);
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
            border: 1px solid #4CAF50;
            display: none;
        }

        .wizard-section.active {
            display: block;
        }

        .section-title {
            color: #4CAF50;
            font-size: 1.4em;
            margin-bottom: 15px;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 5px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #b0b0b0;
            font-weight: 500;
        }

        .label-with-help {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .help-icon {
            background: #2196F3;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: help;
            position: relative;
        }

        .tooltip {
            position: absolute;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
        }

        .help-icon:hover .tooltip {
            opacity: 1;
        }

        input[type="number"], input[type="text"], select, textarea {
            width: 100%;
            padding: 12px;
            background: rgba(60, 60, 80, 0.8);
            border: 1px solid #555;
            border-radius: 8px;
            color: #e0e0e0;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="number"]:focus, input[type="text"]:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        button:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        button:disabled {
            background: #555;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #757575, #616161);
        }

        .btn-secondary:hover {
            background: linear-gradient(45deg, #616161, #757575);
        }

        .btn-warning {
            background: linear-gradient(45deg, #FF9800, #F57C00);
        }

        .btn-warning:hover {
            background: linear-gradient(45deg, #F57C00, #FF9800);
        }

        .wizard-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #555;
        }

        .error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #ffcdd2;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #c8e6c9;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .warning {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid #FF9800;
            color: #ffcc02;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .results-section {
            background: rgba(40, 40, 60, 0.8);
            padding: 25px;
            border-radius: 10px;
            margin-top: 25px;
            border: 1px solid #2196F3;
            display: none;
        }

        .results-section.active {
            display: block;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .summary-card {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            color: white;
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
        }

        .summary-card h3 {
            font-size: 0.9em;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .summary-card .value {
            font-size: 1.8em;
            font-weight: bold;
        }

        .profit-card {
            background: linear-gradient(135deg, #4CAF50, #45a049) !important;
        }

        .loss-card {
            background: linear-gradient(135deg, #f44336, #d32f2f) !important;
        }

        .neutral-card {
            background: linear-gradient(135deg, #757575, #616161) !important;
        }

        .breakdown-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(50, 50, 70, 0.8);
            border-radius: 8px;
            overflow: hidden;
        }

        .breakdown-table th,
        .breakdown-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #555;
        }

        .breakdown-table th {
            background: rgba(76, 175, 80, 0.8);
            color: white;
            font-weight: 600;
        }

        .breakdown-table tr:hover {
            background: rgba(76, 175, 80, 0.1);
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            h1 {
                font-size: 2em;
            }

            .progress-steps {
                flex-direction: column;
                gap: 10px;
            }

            .step {
                margin: 0;
            }

            .input-row {
                grid-template-columns: 1fr;
            }

            .summary-cards {
                grid-template-columns: 1fr;
            }

            .wizard-navigation {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Bradley's Tax Return Calculator!</h1>

        <!-- Navigation -->
        <div class="navigation">
            <a href="cgt-calculator.html" class="nav-link">📈 CGT Calculator</a>
            <a href="ato-tax-calculator.html" class="nav-link">🧮 Tax Return Calculator</a>
        </div>

        <!-- Progress Bar -->
        <div class="progress-bar">
            <div class="progress-steps">
                <div class="step active" id="step-1">1. Personal Info</div>
                <div class="step" id="step-2">2. Income</div>
                <div class="step" id="step-3">3. Deductions</div>
                <div class="step" id="step-4">4. Offsets</div>
                <div class="step" id="step-5">5. Results</div>
            </div>
            <div class="progress-line">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- Step 1: Personal Information -->
        <div class="wizard-section active" id="section-1">
            <h2 class="section-title">📋 Personal Information & Financial Year</h2>

            <div class="input-row">
                <div class="input-group">
                    <label for="financialYear">Financial Year:</label>
                    <select id="financialYear">
                        <option value="2024-25">2024-25 (Current)</option>
                        <option value="2023-24">2023-24</option>
                    </select>
                </div>

                <div class="input-group">
                    <label for="residencyStatus">Tax Residency Status:</label>
                    <select id="residencyStatus">
                        <option value="resident">Australian Resident</option>
                        <option value="foreign">Foreign Resident</option>
                        <option value="working-holiday">Working Holiday Maker</option>
                    </select>
                </div>
            </div>

            <div class="input-row">
                <div class="input-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="hasPrivateHealth">
                        <label for="hasPrivateHealth">I have private health insurance</label>
                        <div class="help-icon">?
                            <div class="tooltip">Required for private health insurance rebate calculation</div>
                        </div>
                    </div>
                </div>

                <div class="input-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="isSeniorPensioner">
                        <label for="isSeniorPensioner">I am a senior or pensioner</label>
                        <div class="help-icon">?
                            <div class="tooltip">Eligible for Seniors and Pensioners Tax Offset (SAPTO)</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="warning">
                <strong>⚠️ Important:</strong> This calculator provides estimates only. For complex situations, consult a qualified tax professional.
                Always refer to official ATO guidance and consider your individual circumstances.
            </div>
        </div>

        <!-- Step 2: Income Sources -->
        <div class="wizard-section" id="section-2">
            <h2 class="section-title">💰 Income Sources</h2>

            <h3 style="color: #4CAF50; margin-bottom: 15px;">Employment Income</h3>
            <div class="input-row">
                <div class="input-group">
                    <div class="label-with-help">
                        <label for="salaryWages">Salary and Wages (Gross):</label>
                        <div class="help-icon">?
                            <div class="tooltip">Total gross salary/wages before tax from all employers</div>
                        </div>
                    </div>
                    <input type="number" id="salaryWages" placeholder="0" min="0" step="1">
                </div>

                <div class="input-group">
                    <div class="label-with-help">
                        <label for="paygWithheld">PAYG Tax Withheld:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Total tax withheld by employers (from payment summaries)</div>
                        </div>
                    </div>
                    <input type="number" id="paygWithheld" placeholder="0" min="0" step="1">
                </div>
            </div>

            <h3 style="color: #4CAF50; margin-bottom: 15px;">Investment Income</h3>
            <div class="input-row">
                <div class="input-group">
                    <div class="label-with-help">
                        <label for="bankInterest">Bank Interest:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Interest from bank accounts, term deposits, etc.</div>
                        </div>
                    </div>
                    <input type="number" id="bankInterest" placeholder="0" min="0" step="0.01">
                </div>

                <div class="input-group">
                    <div class="label-with-help">
                        <label for="dividends">Dividend Income:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Dividends received from Australian companies</div>
                        </div>
                    </div>
                    <input type="number" id="dividends" placeholder="0" min="0" step="0.01">
                </div>
            </div>

            <div class="input-row">
                <div class="input-group">
                    <div class="label-with-help">
                        <label for="frankingCredits">Franking Credits:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Tax credits attached to dividends from Australian companies</div>
                        </div>
                    </div>
                    <input type="number" id="frankingCredits" placeholder="0" min="0" step="0.01">
                </div>

                <div class="input-group">
                    <div class="label-with-help">
                        <label for="rentalIncome">Rental Property Income:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Gross rental income from investment properties</div>
                        </div>
                    </div>
                    <input type="number" id="rentalIncome" placeholder="0" min="0" step="1">
                </div>
            </div>

            <h3 style="color: #4CAF50; margin-bottom: 15px;">Business & Other Income</h3>
            <div class="input-row">
                <div class="input-group">
                    <div class="label-with-help">
                        <label for="businessIncome">Business/Sole Trader Income:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Net profit from business or sole trader activities</div>
                        </div>
                    </div>
                    <input type="number" id="businessIncome" placeholder="0" min="0" step="1">
                </div>

                <div class="input-group">
                    <div class="label-with-help">
                        <label for="governmentPayments">Government Payments:</label>
                        <div class="help-icon">?
                            <div class="tooltip">JobSeeker, pension, family payments, etc.</div>
                        </div>
                    </div>
                    <input type="number" id="governmentPayments" placeholder="0" min="0" step="1">
                </div>
            </div>

            <div class="input-row">
                <div class="input-group">
                    <div class="label-with-help">
                        <label for="capitalGains">Net Capital Gains:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Net capital gains from CGT events (after discount and losses)</div>
                        </div>
                    </div>
                    <input type="number" id="capitalGains" placeholder="0" min="0" step="0.01">
                </div>

                <div class="input-group">
                    <div class="label-with-help">
                        <label for="otherIncome">Other Income:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Any other assessable income not listed above</div>
                        </div>
                    </div>
                    <input type="number" id="otherIncome" placeholder="0" min="0" step="0.01">
                </div>
            </div>
        </div>

        <!-- Step 3: Deductions -->
        <div class="wizard-section" id="section-3">
            <h2 class="section-title">📝 Tax Deductions</h2>

            <h3 style="color: #4CAF50; margin-bottom: 15px;">Work-Related Expenses</h3>
            <div class="input-row">
                <div class="input-group">
                    <div class="label-with-help">
                        <label for="carExpenses">Car Expenses:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Work-related car expenses (logbook or cents per km method)</div>
                        </div>
                    </div>
                    <input type="number" id="carExpenses" placeholder="0" min="0" step="0.01">
                </div>

                <div class="input-group">
                    <div class="label-with-help">
                        <label for="clothingExpenses">Clothing & Uniforms:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Protective clothing, uniforms, occupation-specific clothing</div>
                        </div>
                    </div>
                    <input type="number" id="clothingExpenses" placeholder="0" min="0" step="0.01">
                </div>
            </div>

            <div class="input-row">
                <div class="input-group">
                    <div class="label-with-help">
                        <label for="equipmentExpenses">Equipment & Tools:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Work-related equipment, tools, software, subscriptions</div>
                        </div>
                    </div>
                    <input type="number" id="equipmentExpenses" placeholder="0" min="0" step="0.01">
                </div>

                <div class="input-group">
                    <div class="label-with-help">
                        <label for="travelExpenses">Travel Expenses:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Work-related travel, accommodation, meals</div>
                        </div>
                    </div>
                    <input type="number" id="travelExpenses" placeholder="0" min="0" step="0.01">
                </div>
            </div>

            <h3 style="color: #4CAF50; margin-bottom: 15px;">Work From Home Expenses</h3>
            <div class="checkbox-group">
                <input type="checkbox" id="useWorkFromHome">
                <label for="useWorkFromHome">I worked from home during the financial year</label>
            </div>

            <div id="workFromHomeSection" class="hidden">
                <div class="input-row">
                    <div class="input-group">
                        <label for="wfhMethod">Work From Home Method:</label>
                        <select id="wfhMethod">
                            <option value="fixed-rate">Fixed Rate Method (67c per hour)</option>
                            <option value="actual-cost">Actual Cost Method</option>
                        </select>
                    </div>

                    <div class="input-group" id="wfhHoursGroup">
                        <div class="label-with-help">
                            <label for="wfhHours">Hours Worked From Home:</label>
                            <div class="help-icon">?
                                <div class="tooltip">Total hours worked from home during the financial year</div>
                            </div>
                        </div>
                        <input type="number" id="wfhHours" placeholder="0" min="0" step="1">
                    </div>
                </div>

                <div id="actualCostSection" class="hidden">
                    <div class="input-row">
                        <div class="input-group">
                            <label for="wfhElectricity">Electricity/Gas:</label>
                            <input type="number" id="wfhElectricity" placeholder="0" min="0" step="0.01">
                        </div>

                        <div class="input-group">
                            <label for="wfhInternet">Internet/Phone:</label>
                            <input type="number" id="wfhInternet" placeholder="0" min="0" step="0.01">
                        </div>
                    </div>

                    <div class="input-row">
                        <div class="input-group">
                            <label for="wfhStationery">Stationery/Supplies:</label>
                            <input type="number" id="wfhStationery" placeholder="0" min="0" step="0.01">
                        </div>

                        <div class="input-group">
                            <label for="wfhEquipment">Equipment Depreciation:</label>
                            <input type="number" id="wfhEquipment" placeholder="0" min="0" step="0.01">
                        </div>
                    </div>
                </div>
            </div>

            <h3 style="color: #4CAF50; margin-bottom: 15px;">Investment & Other Deductions</h3>
            <div class="input-row">
                <div class="input-group">
                    <div class="label-with-help">
                        <label for="investmentExpenses">Investment Property Expenses:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Interest, repairs, maintenance, management fees, etc.</div>
                        </div>
                    </div>
                    <input type="number" id="investmentExpenses" placeholder="0" min="0" step="0.01">
                </div>

                <div class="input-group">
                    <div class="label-with-help">
                        <label for="charitableDonations">Charitable Donations:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Donations to registered charities (DGR status required)</div>
                        </div>
                    </div>
                    <input type="number" id="charitableDonations" placeholder="0" min="0" step="0.01">
                </div>
            </div>

            <div class="input-row">
                <div class="input-group">
                    <div class="label-with-help">
                        <label for="taxAgentFees">Tax Agent Fees:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Fees paid to registered tax agents for tax return preparation</div>
                        </div>
                    </div>
                    <input type="number" id="taxAgentFees" placeholder="0" min="0" step="0.01">
                </div>

                <div class="input-group">
                    <div class="label-with-help">
                        <label for="otherDeductions">Other Deductions:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Other allowable deductions not listed above</div>
                        </div>
                    </div>
                    <input type="number" id="otherDeductions" placeholder="0" min="0" step="0.01">
                </div>
            </div>
        </div>

        <!-- Step 4: Tax Offsets -->
        <div class="wizard-section" id="section-4">
            <h2 class="section-title">🎯 Tax Offsets</h2>

            <div class="warning">
                <strong>ℹ️ Note:</strong> Most tax offsets are calculated automatically based on your income and circumstances.
                You only need to provide additional information for specific offsets.
            </div>

            <h3 style="color: #4CAF50; margin-bottom: 15px;">Private Health Insurance</h3>
            <div id="privateHealthSection" class="hidden">
                <div class="input-row">
                    <div class="input-group">
                        <div class="label-with-help">
                            <label for="healthInsurancePremiums">Annual Premiums Paid:</label>
                            <div class="help-icon">?
                                <div class="tooltip">Total private health insurance premiums paid during the year</div>
                            </div>
                        </div>
                        <input type="number" id="healthInsurancePremiums" placeholder="0" min="0" step="0.01">
                    </div>

                    <div class="input-group">
                        <label for="healthInsuranceType">Insurance Type:</label>
                        <select id="healthInsuranceType">
                            <option value="hospital">Hospital Only</option>
                            <option value="extras">Extras Only</option>
                            <option value="combined">Combined Hospital & Extras</option>
                        </select>
                    </div>
                </div>
            </div>

            <h3 style="color: #4CAF50; margin-bottom: 15px;">Seniors and Pensioners</h3>
            <div id="seniorPensionerSection" class="hidden">
                <div class="input-row">
                    <div class="input-group">
                        <label for="pensionerType">Pensioner/Senior Type:</label>
                        <select id="pensionerType">
                            <option value="age-pension">Age Pension</option>
                            <option value="disability-pension">Disability Support Pension</option>
                            <option value="carer-pension">Carer Pension</option>
                            <option value="senior-card">Senior Card Holder</option>
                            <option value="other">Other Eligible Pensioner</option>
                        </select>
                    </div>

                    <div class="input-group">
                        <div class="label-with-help">
                            <label for="rebateIncome">Rebate Income:</label>
                            <div class="help-icon">?
                                <div class="tooltip">Adjusted taxable income plus reportable fringe benefits, etc.</div>
                            </div>
                        </div>
                        <input type="number" id="rebateIncome" placeholder="0" min="0" step="1">
                    </div>
                </div>
            </div>

            <h3 style="color: #4CAF50; margin-bottom: 15px;">Other Offsets</h3>
            <div class="input-row">
                <div class="input-group">
                    <div class="label-with-help">
                        <label for="foreignTaxOffset">Foreign Income Tax Offset:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Tax paid overseas on foreign income</div>
                        </div>
                    </div>
                    <input type="number" id="foreignTaxOffset" placeholder="0" min="0" step="0.01">
                </div>

                <div class="input-group">
                    <div class="label-with-help">
                        <label for="otherOffsets">Other Tax Offsets:</label>
                        <div class="help-icon">?
                            <div class="tooltip">Any other eligible tax offsets not listed above</div>
                        </div>
                    </div>
                    <input type="number" id="otherOffsets" placeholder="0" min="0" step="0.01">
                </div>
            </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="wizard-navigation">
            <button id="prevBtn" class="btn-secondary" onclick="changeStep(-1)" disabled>← Previous</button>
            <div>
                <button id="saveBtn" class="btn-warning" onclick="saveData()">💾 Save Progress</button>
                <button id="loadBtn" class="btn-warning" onclick="loadData()">📂 Load Saved</button>
            </div>
            <button id="nextBtn" onclick="changeStep(1)">Next →</button>
        </div>

        <!-- Results Section -->
        <div class="results-section" id="resultsSection">
            <h2 class="section-title">📊 Tax Calculation Results</h2>

            <div class="summary-cards">
                <div class="summary-card">
                    <h3>Total Taxable Income</h3>
                    <div class="value" id="totalTaxableIncome">$0</div>
                </div>
                <div class="summary-card">
                    <h3>Total Tax Payable</h3>
                    <div class="value" id="totalTaxPayable">$0</div>
                </div>
                <div class="summary-card">
                    <h3>Tax Already Paid</h3>
                    <div class="value" id="taxAlreadyPaid">$0</div>
                </div>
                <div class="summary-card" id="refundCard">
                    <h3>Estimated Refund/Debt</h3>
                    <div class="value" id="estimatedRefund">$0</div>
                </div>
            </div>

            <h3 style="color: #4CAF50; margin-top: 30px;">Detailed Breakdown</h3>
            <table class="breakdown-table" id="breakdownTable">
                <thead>
                    <tr>
                        <th>Component</th>
                        <th>Amount</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody id="breakdownBody">
                </tbody>
            </table>

            <div style="text-align: center; margin-top: 30px;">
                <button onclick="generateTaxReport()" class="btn-warning" style="font-size: 18px; padding: 15px 30px;">
                    📄 Generate PDF Report
                </button>
                <button onclick="startOver()" class="btn-secondary" style="margin-left: 15px;">
                    🔄 Start Over
                </button>
            </div>
        </div>
    </div>

    <script>
        // Australian Tax Brackets and Rates for 2024-25 and 2023-24
        const TAX_BRACKETS = {
            '2024-25': [
                { min: 0, max: 18200, rate: 0, base: 0 },
                { min: 18201, max: 45000, rate: 0.16, base: 0 },
                { min: 45001, max: 135000, rate: 0.30, base: 4288 },
                { min: 135001, max: 190000, rate: 0.37, base: 31288 },
                { min: 190001, max: Infinity, rate: 0.45, base: 51638 }
            ],
            '2023-24': [
                { min: 0, max: 18200, rate: 0, base: 0 },
                { min: 18201, max: 45000, rate: 0.19, base: 0 },
                { min: 45001, max: 120000, rate: 0.325, base: 5092 },
                { min: 120001, max: 180000, rate: 0.37, base: 29467 },
                { min: 180001, max: Infinity, rate: 0.45, base: 51667 }
            ]
        };

        // Medicare Levy and Thresholds
        const MEDICARE_LEVY_RATE = 0.02;
        const MEDICARE_LEVY_THRESHOLDS = {
            '2024-25': { single: 26000, family: 43846 },
            '2023-24': { single: 24276, family: 40939 }
        };

        // Low Income Tax Offset (LITO)
        const LITO = {
            maxOffset: 700,
            phaseOutStart: 37500,
            phaseOutEnd: 45000
        };

        // Seniors and Pensioners Tax Offset (SAPTO)
        const SAPTO = {
            single: { maxOffset: 2230, phaseOutStart: 32279, phaseOutEnd: 50119 },
            couple: { maxOffset: 1602, phaseOutStart: 28974, phaseOutEnd: 41808 }
        };

        // Global variables
        let currentStep = 1;
        let taxData = {};

        // Initialize the calculator
        document.addEventListener('DOMContentLoaded', function() {
            updateProgressBar();
            setupEventListeners();
            loadSavedData();
        });

        function setupEventListeners() {
            // Work from home checkbox
            document.getElementById('useWorkFromHome').addEventListener('change', function() {
                const section = document.getElementById('workFromHomeSection');
                section.classList.toggle('hidden', !this.checked);
            });

            // Work from home method
            document.getElementById('wfhMethod').addEventListener('change', function() {
                const actualCostSection = document.getElementById('actualCostSection');
                const hoursGroup = document.getElementById('wfhHoursGroup');

                if (this.value === 'actual-cost') {
                    actualCostSection.classList.remove('hidden');
                    hoursGroup.classList.add('hidden');
                } else {
                    actualCostSection.classList.add('hidden');
                    hoursGroup.classList.remove('hidden');
                }
            });

            // Private health insurance checkbox
            document.getElementById('hasPrivateHealth').addEventListener('change', function() {
                const section = document.getElementById('privateHealthSection');
                section.classList.toggle('hidden', !this.checked);
            });

            // Senior/pensioner checkbox
            document.getElementById('isSeniorPensioner').addEventListener('change', function() {
                const section = document.getElementById('seniorPensionerSection');
                section.classList.toggle('hidden', !this.checked);
            });

            // Auto-calculate rebate income when other fields change
            const incomeFields = ['salaryWages', 'bankInterest', 'dividends', 'frankingCredits',
                                'rentalIncome', 'businessIncome', 'governmentPayments', 'capitalGains', 'otherIncome'];

            incomeFields.forEach(fieldId => {
                document.getElementById(fieldId).addEventListener('input', updateRebateIncome);
            });
        }

        function updateRebateIncome() {
            if (document.getElementById('isSeniorPensioner').checked) {
                const totalIncome = calculateTotalIncome();
                document.getElementById('rebateIncome').value = totalIncome;
            }
        }

        function changeStep(direction) {
            if (direction === 1 && currentStep < 5) {
                if (validateCurrentStep()) {
                    currentStep++;
                    if (currentStep === 5) {
                        calculateTax();
                    }
                }
            } else if (direction === -1 && currentStep > 1) {
                currentStep--;
            }

            updateWizardDisplay();
            updateProgressBar();
        }

        function validateCurrentStep() {
            // Basic validation - can be enhanced
            const currentSection = document.getElementById(`section-${currentStep}`);
            const requiredFields = currentSection.querySelectorAll('input[required]');

            for (let field of requiredFields) {
                if (!field.value) {
                    showError(`Please fill in all required fields in this section.`);
                    return false;
                }
            }

            return true;
        }

        function updateWizardDisplay() {
            // Hide all sections
            for (let i = 1; i <= 4; i++) {
                document.getElementById(`section-${i}`).classList.remove('active');
                document.getElementById(`step-${i}`).classList.remove('active', 'completed');
            }

            // Show current section
            if (currentStep <= 4) {
                document.getElementById(`section-${currentStep}`).classList.add('active');
                document.getElementById(`step-${currentStep}`).classList.add('active');
            }

            // Mark completed steps
            for (let i = 1; i < currentStep; i++) {
                document.getElementById(`step-${i}`).classList.add('completed');
            }

            // Show/hide results
            const resultsSection = document.getElementById('resultsSection');
            if (currentStep === 5) {
                resultsSection.classList.add('active');
                document.getElementById('step-5').classList.add('active');
            } else {
                resultsSection.classList.remove('active');
            }

            // Update navigation buttons
            document.getElementById('prevBtn').disabled = currentStep === 1;
            document.getElementById('nextBtn').textContent = currentStep === 4 ? 'Calculate Tax' : 'Next →';
        }

        function updateProgressBar() {
            const progress = ((currentStep - 1) / 4) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;
        }

        function calculateTotalIncome() {
            const salaryWages = parseFloat(document.getElementById('salaryWages').value) || 0;
            const bankInterest = parseFloat(document.getElementById('bankInterest').value) || 0;
            const dividends = parseFloat(document.getElementById('dividends').value) || 0;
            const frankingCredits = parseFloat(document.getElementById('frankingCredits').value) || 0;
            const rentalIncome = parseFloat(document.getElementById('rentalIncome').value) || 0;
            const businessIncome = parseFloat(document.getElementById('businessIncome').value) || 0;
            const governmentPayments = parseFloat(document.getElementById('governmentPayments').value) || 0;
            const capitalGains = parseFloat(document.getElementById('capitalGains').value) || 0;
            const otherIncome = parseFloat(document.getElementById('otherIncome').value) || 0;

            return salaryWages + bankInterest + dividends + frankingCredits + rentalIncome +
                   businessIncome + governmentPayments + capitalGains + otherIncome;
        }

        function calculateTotalDeductions() {
            let totalDeductions = 0;

            // Work-related expenses
            totalDeductions += parseFloat(document.getElementById('carExpenses').value) || 0;
            totalDeductions += parseFloat(document.getElementById('clothingExpenses').value) || 0;
            totalDeductions += parseFloat(document.getElementById('equipmentExpenses').value) || 0;
            totalDeductions += parseFloat(document.getElementById('travelExpenses').value) || 0;

            // Work from home expenses
            if (document.getElementById('useWorkFromHome').checked) {
                const wfhMethod = document.getElementById('wfhMethod').value;
                if (wfhMethod === 'fixed-rate') {
                    const hours = parseFloat(document.getElementById('wfhHours').value) || 0;
                    totalDeductions += hours * 0.67; // 67 cents per hour
                } else {
                    totalDeductions += parseFloat(document.getElementById('wfhElectricity').value) || 0;
                    totalDeductions += parseFloat(document.getElementById('wfhInternet').value) || 0;
                    totalDeductions += parseFloat(document.getElementById('wfhStationery').value) || 0;
                    totalDeductions += parseFloat(document.getElementById('wfhEquipment').value) || 0;
                }
            }

            // Other deductions
            totalDeductions += parseFloat(document.getElementById('investmentExpenses').value) || 0;
            totalDeductions += parseFloat(document.getElementById('charitableDonations').value) || 0;
            totalDeductions += parseFloat(document.getElementById('taxAgentFees').value) || 0;
            totalDeductions += parseFloat(document.getElementById('otherDeductions').value) || 0;

            return totalDeductions;
        }

        function calculateIncomeTax(taxableIncome, financialYear) {
            const brackets = TAX_BRACKETS[financialYear];
            let tax = 0;

            for (let bracket of brackets) {
                if (taxableIncome > bracket.min) {
                    const taxableInBracket = Math.min(taxableIncome, bracket.max) - bracket.min + 1;
                    tax = bracket.base + (taxableInBracket * bracket.rate);
                }
            }

            return tax;
        }

        function calculateMedicareLevy(taxableIncome, financialYear) {
            const threshold = MEDICARE_LEVY_THRESHOLDS[financialYear].single;

            if (taxableIncome <= threshold) {
                return 0;
            }

            // Gradual phase-in between threshold and threshold * 1.1
            const phaseInEnd = threshold * 1.1;
            if (taxableIncome <= phaseInEnd) {
                const phaseInAmount = taxableIncome - threshold;
                const phaseInRate = (phaseInAmount / (phaseInEnd - threshold)) * MEDICARE_LEVY_RATE;
                return taxableIncome * phaseInRate;
            }

            return taxableIncome * MEDICARE_LEVY_RATE;
        }

        function calculateLITO(taxableIncome) {
            if (taxableIncome <= LITO.phaseOutStart) {
                return LITO.maxOffset;
            }

            if (taxableIncome >= LITO.phaseOutEnd) {
                return 0;
            }

            const phaseOutRange = LITO.phaseOutEnd - LITO.phaseOutStart;
            const phaseOutAmount = taxableIncome - LITO.phaseOutStart;
            const reduction = (phaseOutAmount / phaseOutRange) * LITO.maxOffset;

            return Math.max(0, LITO.maxOffset - reduction);
        }

        function calculateSAPTO(rebateIncome, isSingle = true) {
            if (!document.getElementById('isSeniorPensioner').checked) {
                return 0;
            }

            const sapto = isSingle ? SAPTO.single : SAPTO.couple;

            if (rebateIncome <= sapto.phaseOutStart) {
                return sapto.maxOffset;
            }

            if (rebateIncome >= sapto.phaseOutEnd) {
                return 0;
            }

            const phaseOutRange = sapto.phaseOutEnd - sapto.phaseOutStart;
            const phaseOutAmount = rebateIncome - sapto.phaseOutStart;
            const reduction = (phaseOutAmount / phaseOutRange) * sapto.maxOffset;

            return Math.max(0, sapto.maxOffset - reduction);
        }

        function calculatePrivateHealthInsuranceRebate(premiums, taxableIncome) {
            if (!document.getElementById('hasPrivateHealth').checked || !premiums) {
                return 0;
            }

            // Simplified rebate calculation - actual rates vary by age and income
            let rebateRate = 0;

            if (taxableIncome <= 90000) {
                rebateRate = 0.24; // 24% for under 65
            } else if (taxableIncome <= 105000) {
                rebateRate = 0.16; // 16% for middle income
            } else if (taxableIncome <= 140000) {
                rebateRate = 0.08; // 8% for higher income
            }
            // No rebate above $140,000

            return premiums * rebateRate;
        }

        function calculateTax() {
            try {
                const financialYear = document.getElementById('financialYear').value;

                // Calculate total income and deductions
                const totalIncome = calculateTotalIncome();
                const totalDeductions = calculateTotalDeductions();
                const taxableIncome = Math.max(0, totalIncome - totalDeductions);

                // Calculate income tax
                const incomeTax = calculateIncomeTax(taxableIncome, financialYear);

                // Calculate Medicare levy
                const medicareLevy = calculateMedicareLevy(taxableIncome, financialYear);

                // Calculate tax offsets
                const lito = calculateLITO(taxableIncome);
                const rebateIncome = parseFloat(document.getElementById('rebateIncome').value) || taxableIncome;
                const sapto = calculateSAPTO(rebateIncome);
                const healthInsurancePremiums = parseFloat(document.getElementById('healthInsurancePremiums').value) || 0;
                const privateHealthRebate = calculatePrivateHealthInsuranceRebate(healthInsurancePremiums, taxableIncome);
                const foreignTaxOffset = parseFloat(document.getElementById('foreignTaxOffset').value) || 0;
                const otherOffsets = parseFloat(document.getElementById('otherOffsets').value) || 0;

                const totalOffsets = lito + sapto + privateHealthRebate + foreignTaxOffset + otherOffsets;

                // Calculate total tax payable
                const grossTax = incomeTax + medicareLevy;
                const totalTaxPayable = Math.max(0, grossTax - totalOffsets);

                // Calculate refund/debt
                const paygWithheld = parseFloat(document.getElementById('paygWithheld').value) || 0;
                const frankingCredits = parseFloat(document.getElementById('frankingCredits').value) || 0;
                const totalCredits = paygWithheld + frankingCredits;
                const refundOrDebt = totalCredits - totalTaxPayable;

                // Store results
                taxData = {
                    totalIncome,
                    totalDeductions,
                    taxableIncome,
                    incomeTax,
                    medicareLevy,
                    grossTax,
                    lito,
                    sapto,
                    privateHealthRebate,
                    foreignTaxOffset,
                    otherOffsets,
                    totalOffsets,
                    totalTaxPayable,
                    paygWithheld,
                    frankingCredits,
                    totalCredits,
                    refundOrDebt,
                    financialYear
                };

                displayResults();

            } catch (error) {
                console.error('Tax calculation error:', error);
                showError('An error occurred during tax calculation. Please check your inputs and try again.');
            }
        }

        function displayResults() {
            // Update summary cards
            document.getElementById('totalTaxableIncome').textContent = formatCurrency(taxData.taxableIncome);
            document.getElementById('totalTaxPayable').textContent = formatCurrency(taxData.totalTaxPayable);
            document.getElementById('taxAlreadyPaid').textContent = formatCurrency(taxData.totalCredits);
            document.getElementById('estimatedRefund').textContent = formatCurrency(Math.abs(taxData.refundOrDebt));

            // Color code the refund/debt card
            const refundCard = document.getElementById('refundCard');
            const refundElement = document.getElementById('estimatedRefund');

            refundCard.classList.remove('profit-card', 'loss-card', 'neutral-card');

            if (taxData.refundOrDebt > 0) {
                refundCard.classList.add('profit-card');
                refundCard.querySelector('h3').textContent = 'Estimated Refund';
            } else if (taxData.refundOrDebt < 0) {
                refundCard.classList.add('loss-card');
                refundCard.querySelector('h3').textContent = 'Estimated Tax Debt';
            } else {
                refundCard.classList.add('neutral-card');
                refundCard.querySelector('h3').textContent = 'No Refund/Debt';
            }

            // Populate breakdown table
            populateBreakdownTable();

            showSuccess('Tax calculation completed successfully!');
        }

        function populateBreakdownTable() {
            const tbody = document.getElementById('breakdownBody');
            tbody.innerHTML = '';

            const breakdownItems = [
                { component: 'Total Income', amount: taxData.totalIncome, details: 'All income sources combined' },
                { component: 'Total Deductions', amount: -taxData.totalDeductions, details: 'All allowable deductions' },
                { component: 'Taxable Income', amount: taxData.taxableIncome, details: 'Income minus deductions' },
                { component: 'Income Tax', amount: taxData.incomeTax, details: `Based on ${taxData.financialYear} tax brackets` },
                { component: 'Medicare Levy (2%)', amount: taxData.medicareLevy, details: 'Medicare levy on taxable income' },
                { component: 'Gross Tax', amount: taxData.grossTax, details: 'Income tax plus Medicare levy' },
                { component: 'Low Income Tax Offset', amount: -taxData.lito, details: 'LITO reduction' },
                { component: 'Seniors/Pensioners Offset', amount: -taxData.sapto, details: 'SAPTO reduction' },
                { component: 'Private Health Rebate', amount: -taxData.privateHealthRebate, details: 'Health insurance rebate' },
                { component: 'Other Offsets', amount: -(taxData.foreignTaxOffset + taxData.otherOffsets), details: 'Foreign tax and other offsets' },
                { component: 'Net Tax Payable', amount: taxData.totalTaxPayable, details: 'Final tax after all offsets' },
                { component: 'PAYG Tax Withheld', amount: -taxData.paygWithheld, details: 'Tax already paid by employers' },
                { component: 'Franking Credits', amount: -taxData.frankingCredits, details: 'Credits from dividend franking' }
            ];

            breakdownItems.forEach(item => {
                if (item.amount !== 0) {
                    const row = tbody.insertRow();
                    row.innerHTML = `
                        <td>${item.component}</td>
                        <td class="${item.amount >= 0 ? (item.component.includes('Tax') || item.component.includes('Levy') ? 'loss' : 'gain') : 'gain'}">
                            ${formatCurrency(Math.abs(item.amount))}
                        </td>
                        <td>${item.details}</td>
                    `;
                }
            });

            // Add final result row
            const finalRow = tbody.insertRow();
            finalRow.style.fontWeight = 'bold';
            finalRow.style.borderTop = '2px solid #4CAF50';
            finalRow.innerHTML = `
                <td>${taxData.refundOrDebt >= 0 ? 'Estimated Refund' : 'Estimated Tax Debt'}</td>
                <td class="${taxData.refundOrDebt >= 0 ? 'gain' : 'loss'}">
                    ${formatCurrency(Math.abs(taxData.refundOrDebt))}
                </td>
                <td>${taxData.refundOrDebt >= 0 ? 'Amount to be refunded' : 'Additional tax to pay'}</td>
            `;
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            document.querySelector('.container').insertBefore(errorDiv, document.querySelector('.wizard-section, .results-section'));
            setTimeout(() => errorDiv.remove(), 5000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            document.querySelector('.container').insertBefore(successDiv, document.querySelector('.results-section'));
            setTimeout(() => successDiv.remove(), 5000);
        }

        function saveData() {
            const formData = {};
            const inputs = document.querySelectorAll('input, select');

            inputs.forEach(input => {
                if (input.type === 'checkbox') {
                    formData[input.id] = input.checked;
                } else {
                    formData[input.id] = input.value;
                }
            });

            localStorage.setItem('atoTaxCalculatorData', JSON.stringify(formData));
            showSuccess('Data saved successfully!');
        }

        function loadData() {
            const savedData = localStorage.getItem('atoTaxCalculatorData');
            if (savedData) {
                const formData = JSON.parse(savedData);

                Object.keys(formData).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        if (element.type === 'checkbox') {
                            element.checked = formData[key];
                        } else {
                            element.value = formData[key];
                        }
                    }
                });

                // Trigger change events to update dependent sections
                document.getElementById('useWorkFromHome').dispatchEvent(new Event('change'));
                document.getElementById('wfhMethod').dispatchEvent(new Event('change'));
                document.getElementById('hasPrivateHealth').dispatchEvent(new Event('change'));
                document.getElementById('isSeniorPensioner').dispatchEvent(new Event('change'));

                showSuccess('Data loaded successfully!');
            } else {
                showError('No saved data found.');
            }
        }

        function loadSavedData() {
            // Auto-load data on page load if available
            const savedData = localStorage.getItem('atoTaxCalculatorData');
            if (savedData) {
                loadData();
            }
        }

        function startOver() {
            if (confirm('Are you sure you want to start over? This will clear all entered data.')) {
                // Clear all form fields
                const inputs = document.querySelectorAll('input, select');
                inputs.forEach(input => {
                    if (input.type === 'checkbox') {
                        input.checked = false;
                    } else {
                        input.value = '';
                    }
                });

                // Reset to first step
                currentStep = 1;
                updateWizardDisplay();
                updateProgressBar();

                // Hide conditional sections
                document.getElementById('workFromHomeSection').classList.add('hidden');
                document.getElementById('actualCostSection').classList.add('hidden');
                document.getElementById('privateHealthSection').classList.add('hidden');
                document.getElementById('seniorPensionerSection').classList.add('hidden');

                // Clear saved data
                localStorage.removeItem('atoTaxCalculatorData');

                showSuccess('Calculator reset successfully!');
            }
        }

        function generateTaxReport() {
            if (!taxData || Object.keys(taxData).length === 0) {
                showError('No tax calculation data available. Please complete the calculation first.');
                return;
            }

            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                // Set up document properties
                doc.setProperties({
                    title: 'ATO Tax Return Calculator Report',
                    subject: 'Australian Tax Calculation Summary',
                    author: 'ATO Tax Calculator',
                    creator: 'ATO Tax Return Calculator'
                });

                let yPosition = 20;
                const pageHeight = doc.internal.pageSize.height;
                const margin = 20;
                const lineHeight = 7;

                // Helper function to check if we need a new page
                function checkNewPage(requiredSpace = 20) {
                    if (yPosition + requiredSpace > pageHeight - margin) {
                        doc.addPage();
                        yPosition = 20;
                        return true;
                    }
                    return false;
                }

                // Title
                doc.setFontSize(20);
                doc.setFont('helvetica', 'bold');
                doc.text('Australian Tax Return Calculator Report', margin, yPosition);
                yPosition += 15;

                // Financial year and date
                doc.setFontSize(12);
                doc.setFont('helvetica', 'normal');
                doc.text(`Financial Year: ${taxData.financialYear}`, margin, yPosition);
                yPosition += lineHeight;
                doc.text(`Generated: ${new Date().toLocaleDateString('en-AU')}`, margin, yPosition);
                yPosition += 15;

                // Summary section
                checkNewPage(60);
                doc.setFontSize(16);
                doc.setFont('helvetica', 'bold');
                doc.text('Tax Calculation Summary', margin, yPosition);
                yPosition += 10;

                doc.setFontSize(12);
                doc.setFont('helvetica', 'normal');

                const summaryData = [
                    ['Total Taxable Income', formatCurrency(taxData.taxableIncome)],
                    ['Total Tax Payable', formatCurrency(taxData.totalTaxPayable)],
                    ['Tax Already Paid', formatCurrency(taxData.totalCredits)],
                    [taxData.refundOrDebt >= 0 ? 'Estimated Refund' : 'Estimated Tax Debt',
                     formatCurrency(Math.abs(taxData.refundOrDebt))]
                ];

                doc.autoTable({
                    startY: yPosition,
                    head: [['Component', 'Amount']],
                    body: summaryData,
                    theme: 'grid',
                    headStyles: { fillColor: [76, 175, 80] },
                    margin: { left: margin, right: margin }
                });

                yPosition = doc.lastAutoTable.finalY + 15;

                // Detailed breakdown
                checkNewPage(80);
                doc.setFontSize(16);
                doc.setFont('helvetica', 'bold');
                doc.text('Detailed Breakdown', margin, yPosition);
                yPosition += 10;

                const breakdownData = [
                    ['Total Income', formatCurrency(taxData.totalIncome)],
                    ['Total Deductions', formatCurrency(taxData.totalDeductions)],
                    ['Taxable Income', formatCurrency(taxData.taxableIncome)],
                    ['Income Tax', formatCurrency(taxData.incomeTax)],
                    ['Medicare Levy', formatCurrency(taxData.medicareLevy)],
                    ['Low Income Tax Offset', formatCurrency(taxData.lito)],
                    ['Seniors/Pensioners Offset', formatCurrency(taxData.sapto)],
                    ['Private Health Rebate', formatCurrency(taxData.privateHealthRebate)],
                    ['Other Offsets', formatCurrency(taxData.foreignTaxOffset + taxData.otherOffsets)],
                    ['PAYG Tax Withheld', formatCurrency(taxData.paygWithheld)],
                    ['Franking Credits', formatCurrency(taxData.frankingCredits)]
                ];

                doc.autoTable({
                    startY: yPosition,
                    head: [['Component', 'Amount']],
                    body: breakdownData,
                    theme: 'grid',
                    headStyles: { fillColor: [33, 150, 243] },
                    margin: { left: margin, right: margin }
                });

                yPosition = doc.lastAutoTable.finalY + 15;

                // Disclaimer
                checkNewPage(40);
                doc.setFontSize(10);
                doc.setFont('helvetica', 'italic');
                const disclaimer = 'DISCLAIMER: This calculation is an estimate only and should not be relied upon for tax planning or lodgment purposes. ' +
                                 'Consult a qualified tax professional for advice specific to your circumstances. ' +
                                 'Always refer to official ATO guidance and current legislation.';

                const disclaimerLines = doc.splitTextToSize(disclaimer, 170);
                doc.text(disclaimerLines, margin, yPosition);

                // Save the PDF
                const fileName = `ATO_Tax_Calculator_${taxData.financialYear}_${new Date().toISOString().split('T')[0]}.pdf`;
                doc.save(fileName);

                showSuccess('PDF report generated successfully!');

            } catch (error) {
                console.error('PDF generation error:', error);
                showError('Failed to generate PDF report. Please try again.');
            }
        }
    </script>
</body>
</html>
