# Portfolio Management & CGT Calculator Integration Guide

## Overview

The Portfolio Management system integrates seamlessly with the existing CGT Calculator to provide a comprehensive solution for tracking investments and calculating capital gains tax. This system allows you to:

- **Visualize your portfolio** with interactive charts
- **Mark transactions as sold** to improve CGT calculation accuracy
- **Sync data** between Portfolio Manager and CGT Calculator
- **Generate reports** for tax compliance

## System Architecture

### Components
1. **Portfolio Manager** (`portfolio-manager.html`) - Main portfolio dashboard
2. **CGT Calculator** (`cgt-calculator.html`) - Enhanced with portfolio integration
3. **ATO Tax Calculator** (`ato-tax-calculator.html`) - Updated navigation
4. **Portfolio Folder** - Contains data files and documentation

### Data Flow
```
Portfolio CSV → Portfolio Manager → localStorage → CGT Calculator → Tax Reports
```

## Getting Started

### 1. Prepare Your Data
- Export transaction data from your broker as CSV
- Ensure the CSV contains required columns (see Portfolio/README.md)
- Save the file in the Portfolio folder

### 2. Load Portfolio Data
1. Open `portfolio-manager.html`
2. Click "Upload Portfolio CSV File"
3. Select your CSV file
4. Click "📊 Load Portfolio"

### 3. Review and Mark Transactions
- View your portfolio allocation in the pie chart
- Check the holdings table for accuracy
- Use checkboxes to mark BUY transactions as "sold"
- This excludes them from future CGT calculations

### 4. Sync with CGT Calculator
1. Click "📤 Export to CGT Calculator" in Portfolio Manager
2. Open CGT Calculator
3. Click "📊 Import from Portfolio"
4. Review and calculate CGT

## Key Features

### Visual Dashboard
- **Portfolio Allocation Pie Chart**: Shows percentage breakdown by stock
- **Gains/Losses Bar Chart**: Displays unrealized gains/losses by holding
- **Summary Cards**: Total value, cost base, unrealized gains, number of holdings

### Transaction Management
- **Sold Marking**: Checkbox system for BUY transactions only
- **Real-time Updates**: Charts and tables update immediately
- **Persistent Storage**: Sold states saved in browser localStorage

### Integration Features
- **Data Export/Import**: Seamless data transfer between tools
- **Sync Status**: Visual indicators of integration status
- **Automatic Updates**: Changes in one tool reflect in the other

### Reporting
- **Portfolio Reports**: PDF generation with holdings summary
- **CGT Reports**: Enhanced with portfolio data
- **ATO Compliance**: All reports meet Australian tax requirements

## File Structure

```
CGT Calculator/
├── portfolio-manager.html          # Main portfolio dashboard
├── cgt-calculator.html             # Enhanced CGT calculator
├── ato-tax-calculator.html         # Tax return calculator
├── public/                         # Public versions of files
│   ├── portfolio-manager.html
│   ├── cgt-calculator.html
│   └── ato-tax-calculator.html
├── Portfolio/                      # Portfolio data folder
│   ├── README.md                   # Data format documentation
│   ├── examples/
│   │   └── sample-portfolio.csv    # Example data file
│   └── backups/                    # Backup location
└── PORTFOLIO_INTEGRATION_GUIDE.md  # This file
```

## Data Format Requirements

### CSV Columns (Required)
| Column | Description | Example |
|--------|-------------|---------|
| Date | Transaction date | 15/03/2024 |
| Code | Stock code | CBA.ASX |
| Action | BUY or SELL | BUY |
| Quantity | Number of shares | 100 |
| Avg. price | Price per share | 95.250 |
| Fees | Brokerage fees | 9.50 |
| Settl. value | Total settlement | 9534.50 |

### Optional Columns
- Confirmation No. (auto-generated if missing)

## Integration Workflow

### Typical Usage Pattern
1. **Import Data**: Load CSV into Portfolio Manager
2. **Review Holdings**: Check portfolio allocation and values
3. **Mark Sold Shares**: Use checkboxes for shares already disposed
4. **Export to CGT**: Send data to CGT Calculator
5. **Calculate Tax**: Run CGT calculations with marked exclusions
6. **Generate Reports**: Create PDF reports for tax filing

### Data Synchronization
- **localStorage**: Used for browser-based data persistence
- **Sold States**: Synchronized between Portfolio Manager and CGT Calculator
- **Transaction Data**: Shared via export/import functions
- **Real-time Updates**: Changes reflect immediately across tools

## Advanced Features

### Chart Customization
- **Color Coding**: Automatic color assignment for different stocks
- **Interactive Tooltips**: Hover for detailed information
- **Responsive Design**: Works on desktop and mobile devices

### Error Handling
- **CSV Validation**: Automatic format checking
- **Data Recovery**: Backup and restore functionality
- **User Feedback**: Clear error and success messages

### Performance Optimization
- **Lazy Loading**: Charts load only when needed
- **Efficient Updates**: Only changed data is reprocessed
- **Memory Management**: Proper cleanup of chart instances

## Troubleshooting

### Common Issues
1. **CSV Format Errors**: Check column names and data types
2. **Date Parsing**: Ensure consistent date format (DD/MM/YYYY)
3. **Missing Data**: Verify all required columns are present
4. **Sync Issues**: Clear browser cache and reload

### Browser Compatibility
- **Chrome**: Fully supported
- **Firefox**: Fully supported
- **Safari**: Fully supported
- **Edge**: Fully supported

### Data Limits
- **File Size**: Recommended maximum 10MB CSV files
- **Transactions**: Tested with up to 10,000 transactions
- **Performance**: Optimized for typical retail portfolios

## Security & Privacy

### Data Protection
- **Local Processing**: All data processed in browser
- **No External Servers**: No data sent to third parties
- **Browser Storage**: Uses secure localStorage API
- **File Access**: Only user-selected files are accessed

### Best Practices
- **Regular Backups**: Save copies of your CSV files
- **Data Validation**: Always verify imported data accuracy
- **Browser Updates**: Keep browser updated for security
- **File Security**: Store CSV files in secure locations

## Support & Updates

### Getting Help
- Check browser console for detailed error messages
- Verify CSV format against provided examples
- Review this documentation for common solutions

### Future Enhancements
- Real-time stock price integration
- Additional chart types and visualizations
- Enhanced reporting features
- Mobile app development

## Version Information
- **Version**: 1.0
- **Release Date**: 2024
- **Compatibility**: Modern browsers with ES6 support
- **Dependencies**: Chart.js, jsPDF

---

For technical support or feature requests, please refer to the project documentation or contact the development team.
